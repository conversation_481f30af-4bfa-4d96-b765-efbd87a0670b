const fetch = require('node-fetch');

async function testLogin() {
  console.log('🔐 Testing Admin Login...\n');

  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    console.log('Response Status:', response.status);
    
    const data = await response.json();
    console.log('Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ Admin login successful!');
      console.log(`Welcome ${data.name} (${data.role})`);
    } else {
      console.log('\n❌ Admin login failed!');
      console.log('Error:', data.error);
    }

  } catch (error) {
    console.error('❌ Login test failed:', error.message);
  }
}

// Test with wrong credentials
async function testWrongLogin() {
  console.log('\n🔐 Testing Wrong Credentials...\n');

  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    });

    console.log('Response Status:', response.status);
    
    const data = await response.json();
    console.log('Response Data:', JSON.stringify(data, null, 2));

    if (response.status === 401) {
      console.log('\n✅ Wrong credentials properly rejected!');
    } else {
      console.log('\n❌ Security issue: wrong credentials accepted!');
    }

  } catch (error) {
    console.error('❌ Wrong login test failed:', error.message);
  }
}

async function runTests() {
  await testLogin();
  await testWrongLogin();
  console.log('\n🎉 Login tests completed!');
}

runTests();
