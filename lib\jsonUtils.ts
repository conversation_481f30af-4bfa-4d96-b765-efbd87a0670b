import fs from 'fs';
import path from 'path';

/**
 * Reads a JSON file from the data directory with dynamic path handling
 * @param filePath - The path to the JSON file relative to the data directory
 * @returns The parsed JSON data
 * @throws Error if file cannot be read or parsed
 */
export async function readJsonFile<T>(filePath: string): Promise<T> {
  try {
    // Ensure the path is relative to the data directory
    const fullPath = path.join(process.cwd(), 'data', filePath);
    
    // Read the file
    const fileContent = await fs.promises.readFile(fullPath, 'utf-8');
    
    // Parse and return the JSON data
    return JSON.parse(fileContent) as T;
  } catch (error) {
    throw new Error(`Error reading JSON file at ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Writes data to a JSON file in the data directory with dynamic path handling
 * @param filePath - The path to the JSON file relative to the data directory
 * @param data - The data to write to the file
 * @throws Error if file cannot be written
 */
export async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  try {
    // Ensure the path is relative to the data directory
    const fullPath = path.join(process.cwd(), 'data', filePath);
    
    // Create directory if it doesn't exist
    const dir = path.dirname(fullPath);
    await fs.promises.mkdir(dir, { recursive: true });
    
    // Write the data to the file
    await fs.promises.writeFile(fullPath, JSON.stringify(data, null, 2), 'utf-8');
  } catch (error) {
    throw new Error(`Error writing JSON file at ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Checks if a JSON file exists in the data directory
 * @param filePath - The path to the JSON file relative to the data directory
 * @returns boolean indicating if the file exists
 */
export async function jsonFileExists(filePath: string): Promise<boolean> {
  try {
    const fullPath = path.join(process.cwd(), 'data', filePath);
    await fs.promises.access(fullPath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Deletes a JSON file from the data directory
 * @param filePath - The path to the JSON file relative to the data directory
 * @throws Error if file cannot be deleted
 */
export async function deleteJsonFile(filePath: string): Promise<void> {
  try {
    const fullPath = path.join(process.cwd(), 'data', filePath);
    await fs.promises.unlink(fullPath);
  } catch (error) {
    throw new Error(`Error deleting JSON file at ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
} 