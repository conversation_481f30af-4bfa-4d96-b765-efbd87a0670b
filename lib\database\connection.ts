import path from 'path';
import fs from 'fs';

// Type definition for Database
type Database = any;

// Database file path
const DB_PATH = path.join(process.cwd(), 'data', 'store.db');

// Create database connection
let db: Database | null = null;
let isInitializing = false;

// Mock database for fallback when better-sqlite3 is not available
function createMockDatabase() {
  console.warn('Using mock database - data will not persist');
  return {
    pragma: () => {},
    exec: () => {},
    prepare: (_sql: string) => ({
      run: (..._params: any[]) => ({ changes: 1, lastInsertRowid: 1 }),
      get: (..._params: any[]) => null,
      all: (..._params: any[]) => [],
    }),
    transaction: (fn: Function) => fn,
    close: () => {},
  };
}

// Server-side only database initialization
function initializeDatabase() {
  if (typeof window !== 'undefined') {
    throw new Error('Database can only be used on the server side');
  }

  if (db || isInitializing) {
    return db;
  }

  isInitializing = true;

  try {
    // Try to load better-sqlite3, fallback to mock if not available
    let Database;
    try {
      Database = require('better-sqlite3');
    } catch (e) {
      console.warn('better-sqlite3 not available, using fallback mode');
      // Return a mock database for development
      isInitializing = false;
      db = createMockDatabase();
      return db;
    }

    // Ensure data directory exists
    const dataDir = path.dirname(DB_PATH);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new Database(DB_PATH);

    // Enable foreign key constraints
    db.pragma('foreign_keys = ON');

    // Set journal mode to WAL for better performance
    db.pragma('journal_mode = WAL');

    // Initialize database schema
    initializeSchema();

    isInitializing = false;
    return db;
  } catch (error) {
    isInitializing = false;
    console.error('Failed to initialize database:', error);
    // Fallback to mock database
    db = createMockDatabase();
    return db;
  }
}

export function getDatabase(): Database {
  if (!db) {
    initializeDatabase();
  }
  return db;
}

function initializeSchema() {
  if (!db) return;

  const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql');

  if (fs.existsSync(schemaPath)) {
    const schema = fs.readFileSync(schemaPath, 'utf-8');

    // Split schema by semicolons and execute each statement
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      try {
        db.exec(statement);
      } catch (error) {
        console.error('Error executing schema statement:', statement);
        console.error(error);
      }
    }
  }
}

// Close database connection
export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}

// Utility function to run migrations
export function runMigration(migrationSql: string) {
  const database = getDatabase();

  try {
    database.exec(migrationSql);
    console.log('Migration executed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Transaction wrapper
export function withTransaction<T>(callback: (db: Database) => T): T {
  const database = getDatabase();

  const transaction = database.transaction(() => {
    return callback(database);
  });

  return transaction();
}

// Graceful shutdown
process.on('exit', closeDatabase);
process.on('SIGINT', closeDatabase);
process.on('SIGTERM', closeDatabase);
