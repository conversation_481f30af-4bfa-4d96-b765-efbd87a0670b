// Manual validation script to check if the migration would work
const fs = require('fs');
const path = require('path');

console.log('🔍 Manual Migration Validation\n');

// Check if data files exist
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

console.log('📁 Checking data files...');
let allFilesExist = true;

for (const [name, filePath] of Object.entries(FILES)) {
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`✓ ${name}.json exists (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${name}.json missing`);
    allFilesExist = false;
  }
}

if (!allFilesExist) {
  console.log('\n❌ Some data files are missing. Cannot proceed with migration.');
  process.exit(1);
}

// Check if schema file exists
const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql');
if (fs.existsSync(schemaPath)) {
  console.log('✓ Database schema file exists');
} else {
  console.log('❌ Database schema file missing');
  process.exit(1);
}

// Check if database models exist
const modelsPath = path.join(process.cwd(), 'lib', 'database', 'models.ts');
if (fs.existsSync(modelsPath)) {
  console.log('✓ Database models file exists');
} else {
  console.log('❌ Database models file missing');
  process.exit(1);
}

// Check if connection file exists
const connectionPath = path.join(process.cwd(), 'lib', 'database', 'connection.ts');
if (fs.existsSync(connectionPath)) {
  console.log('✓ Database connection file exists');
} else {
  console.log('❌ Database connection file missing');
  process.exit(1);
}

// Read and validate data structure
function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

console.log('\n📊 Data validation...');

// Validate categories
const categories = readJsonFile(FILES.categories);
console.log(`Categories: ${categories.length} records`);
if (categories.length > 0) {
  const sample = categories[0];
  const hasRequiredFields = sample.id && sample.name && sample.slug;
  console.log(`  Sample: ${sample.name} (${hasRequiredFields ? 'valid' : 'invalid'})`);
}

// Validate users
const users = readJsonFile(FILES.users);
console.log(`Users: ${users.length} records`);
if (users.length > 0) {
  const sample = users[0];
  const hasRequiredFields = sample.id && sample.name && sample.email && sample.password;
  console.log(`  Sample: ${sample.name} (${hasRequiredFields ? 'valid' : 'invalid'})`);
}

// Validate products
const products = readJsonFile(FILES.products);
const promotionalProducts = readJsonFile(FILES.promotionalProducts);
console.log(`Products: ${products.length} regular + ${promotionalProducts.length} promotional = ${products.length + promotionalProducts.length} total`);
if (products.length > 0) {
  const sample = products[0];
  const hasRequiredFields = sample.id && sample.name && sample.price;
  console.log(`  Sample: ${sample.name} (${hasRequiredFields ? 'valid' : 'invalid'})`);
}

// Validate reviews
const reviews = readJsonFile(FILES.reviews);
console.log(`Reviews: ${reviews.length} records`);
if (reviews.length > 0) {
  const sample = reviews[0];
  const hasRequiredFields = sample.id && sample.productId && sample.userId && sample.rating;
  console.log(`  Sample: Review ${sample.id} (${hasRequiredFields ? 'valid' : 'invalid'})`);
}

// Validate orders
const orders = readJsonFile(FILES.orders);
console.log(`Orders: ${orders.length} records`);
if (orders.length > 0) {
  const sample = orders[0];
  const hasRequiredFields = sample.id && sample.userId && sample.totalAmount;
  const hasItems = sample.items && Array.isArray(sample.items) && sample.items.length > 0;
  console.log(`  Sample: Order ${sample.id} (${hasRequiredFields ? 'valid' : 'invalid'}, ${hasItems ? 'has items' : 'no items'})`);
}

// Validate transactions
const transactions = readJsonFile(FILES.transactions);
console.log(`Transactions: ${transactions.length} records`);
if (transactions.length > 0) {
  const sample = transactions[0];
  const hasRequiredFields = sample.id && sample.orderId && sample.userId && sample.amount;
  console.log(`  Sample: Transaction ${sample.id} (${hasRequiredFields ? 'valid' : 'invalid'})`);
}

// Check relationships
console.log('\n🔗 Relationship validation...');

// Check if all products reference valid categories
const categoryIds = categories.map(c => c.id);
const allProducts = [...products, ...promotionalProducts];
const invalidProductCategories = allProducts.filter(p => 
  p.category && !categoryIds.includes(p.category.id || p.category.slug)
);
console.log(`Product-Category relationships: ${invalidProductCategories.length} invalid out of ${allProducts.length}`);

// Check if all reviews reference valid products and users
const productIds = allProducts.map(p => p.id);
const userIds = users.map(u => u.id);
const invalidReviewProducts = reviews.filter(r => !productIds.includes(r.productId));
const invalidReviewUsers = reviews.filter(r => !userIds.includes(r.userId));
console.log(`Review-Product relationships: ${invalidReviewProducts.length} invalid out of ${reviews.length}`);
console.log(`Review-User relationships: ${invalidReviewUsers.length} invalid out of ${reviews.length}`);

// Check if all orders reference valid users
const invalidOrderUsers = orders.filter(o => !userIds.includes(o.userId));
console.log(`Order-User relationships: ${invalidOrderUsers.length} invalid out of ${orders.length}`);

// Check if all transactions reference valid orders and users
const orderIds = orders.map(o => o.id);
const invalidTransactionOrders = transactions.filter(t => !orderIds.includes(t.orderId));
const invalidTransactionUsers = transactions.filter(t => !userIds.includes(t.userId));
console.log(`Transaction-Order relationships: ${invalidTransactionOrders.length} invalid out of ${transactions.length}`);
console.log(`Transaction-User relationships: ${invalidTransactionUsers.length} invalid out of ${transactions.length}`);

// Check API files
console.log('\n🔌 API files validation...');

const apiPaths = [
  'app/api/products/route.ts',
  'app/api/products/promotional/route.ts',
  'app/api/products/[id]/route.ts',
  'app/api/auth/login/route.ts',
  'app/api/auth/register/route.ts',
  'app/api/users/update/route.ts',
  'app/api/users/update-password/route.ts',
  'app/api/orders/route.ts',
  'app/api/orders/[id]/route.ts',
  'app/api/admin/products/route.ts',
  'app/api/admin/users/route.ts',
  'app/api/admin/categories/route.ts',
  'app/api/admin/reviews/route.ts',
  'app/api/admin/transactions/route.ts'
];

let apiFilesUpdated = 0;
for (const apiPath of apiPaths) {
  if (fs.existsSync(apiPath)) {
    const content = fs.readFileSync(apiPath, 'utf-8');
    if (content.includes('database/models') || content.includes('database/connection')) {
      apiFilesUpdated++;
      console.log(`✓ ${apiPath} - Updated for SQLite`);
    } else {
      console.log(`⚠️  ${apiPath} - Still using JSON files`);
    }
  } else {
    console.log(`❌ ${apiPath} - Missing`);
  }
}

console.log(`\nAPI Migration Status: ${apiFilesUpdated}/${apiPaths.length} files updated`);

// Summary
console.log('\n📋 Migration Readiness Summary:');
console.log(`✓ Data files: ${Object.keys(FILES).length}/${Object.keys(FILES).length} present`);
console.log(`✓ Database schema: Available`);
console.log(`✓ Database models: Available`);
console.log(`✓ Database connection: Available`);
console.log(`✓ API files: ${apiFilesUpdated}/${apiPaths.length} updated`);

const totalRecords = categories.length + users.length + allProducts.length + reviews.length + orders.length + transactions.length;
console.log(`📊 Total records to migrate: ${totalRecords}`);

if (apiFilesUpdated === apiPaths.length) {
  console.log('\n🎉 All systems ready for migration!');
  console.log('\nNext steps:');
  console.log('1. Install dependencies: npm install');
  console.log('2. Run migration: npm run migrate');
  console.log('3. Test APIs: npm run test:migration');
  console.log('4. Start development server: npm run dev');
} else {
  console.log('\n⚠️  Some API files still need to be updated for SQLite.');
}

console.log('\n✅ Manual validation completed!');
