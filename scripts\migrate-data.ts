#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';
import { getDatabase, withTransaction } from '../lib/database/connection';
import {
  CategoryModel,
  UserModel,
  UserAddressModel,
  ProductModel,
  ReviewModel,
  OrderModel,
  OrderItemModel,
  OrderShippingAddressModel,
  TransactionModel
} from '../lib/database/models';

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

// Helper function to read JSON file
function readJsonFile<T>(filePath: string): T[] {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return [];
  }
}

// Migration functions
async function migrateCategories() {
  console.log('Migrating categories...');
  const categories = readJsonFile<any>(FILES.categories);
  
  for (const category of categories) {
    try {
      CategoryModel.create({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
        status: category.status || 'active'
      });
      console.log(`✓ Migrated category: ${category.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate category ${category.id}:`, error);
    }
  }
}

async function migrateUsers() {
  console.log('Migrating users...');
  const users = readJsonFile<any>(FILES.users);
  
  for (const user of users) {
    try {
      // Create user
      const dbUser = UserModel.create({
        id: user.id,
        name: user.name,
        email: user.email,
        password: user.password,
        phone: user.phone,
        role: user.role || 'customer',
        status: user.status || 'active'
      });
      
      // Create user address if exists
      if (user.address) {
        UserAddressModel.create({
          user_id: user.id,
          full_name: user.address.fullName,
          phone: user.address.phone,
          address: user.address.address,
          city: user.address.city,
          district: user.address.district,
          ward: user.address.ward,
          is_default: true
        });
      }
      
      console.log(`✓ Migrated user: ${user.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate user ${user.id}:`, error);
    }
  }
}

async function migrateProducts() {
  console.log('Migrating products...');
  
  // Migrate regular products
  const products = readJsonFile<any>(FILES.products);
  for (const product of products) {
    try {
      ProductModel.create({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        original_price: product.originalPrice,
        image: product.image,
        images: JSON.stringify(product.images || []),
        category_id: product.category?.id || product.category?.slug || 'unknown',
        stock: product.stock || 0,
        rating: product.rating || 0,
        review_count: product.reviewCount || 0,
        sold_count: product.soldCount || 0,
        featured: product.featured || false,
        is_promotional: false,
        promotion_ends: null
      });
      console.log(`✓ Migrated product: ${product.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate product ${product.id}:`, error);
    }
  }
  
  // Migrate promotional products
  const promotionalProducts = readJsonFile<any>(FILES.promotionalProducts);
  for (const product of promotionalProducts) {
    try {
      ProductModel.create({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        original_price: product.originalPrice,
        image: product.image,
        images: JSON.stringify(product.images || []),
        category_id: product.category?.id || product.category?.slug || 'unknown',
        stock: product.stock || 0,
        rating: product.rating || 0,
        review_count: product.reviewCount || 0,
        sold_count: product.soldCount || 0,
        featured: product.featured || false,
        is_promotional: true,
        promotion_ends: product.promotionEnds || null
      });
      console.log(`✓ Migrated promotional product: ${product.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate promotional product ${product.id}:`, error);
    }
  }
}

async function migrateReviews() {
  console.log('Migrating reviews...');
  const reviews = readJsonFile<any>(FILES.reviews);

  for (const review of reviews) {
    try {
      ReviewModel.create({
        id: review.id,
        product_id: review.productId,
        user_id: review.userId,
        rating: review.rating,
        comment: review.comment,
        status: review.status || 'pending'
      });
      console.log(`✓ Migrated review: ${review.id}`);
    } catch (error) {
      console.error(`✗ Failed to migrate review ${review.id}:`, error);
    }
  }
}

async function migrateOrders() {
  console.log('Migrating orders...');
  const orders = readJsonFile<any>(FILES.orders);

  for (const order of orders) {
    try {
      // Create order
      const dbOrder = OrderModel.create({
        id: order.id,
        user_id: order.userId,
        status: order.status || 'pending',
        total_amount: order.totalAmount,
        payment_method: order.paymentMethod
      });

      // Create shipping address
      if (order.shippingAddress) {
        OrderShippingAddressModel.create({
          order_id: order.id,
          full_name: order.shippingAddress.fullName,
          phone: order.shippingAddress.phone,
          address: order.shippingAddress.address,
          city: order.shippingAddress.city,
          district: order.shippingAddress.district,
          ward: order.shippingAddress.ward
        });
      }

      // Create order items
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          OrderItemModel.create({
            id: item.id,
            order_id: order.id,
            product_id: item.productId,
            quantity: item.quantity,
            price: item.price,
            product_name: item.product?.name || 'Unknown Product',
            product_image: item.product?.image || null
          });
        }
      }

      console.log(`✓ Migrated order: ${order.id}`);
    } catch (error) {
      console.error(`✗ Failed to migrate order ${order.id}:`, error);
    }
  }
}

async function migrateTransactions() {
  console.log('Migrating transactions...');
  const transactions = readJsonFile<any>(FILES.transactions);

  for (const transaction of transactions) {
    try {
      TransactionModel.create({
        id: transaction.id,
        order_id: transaction.orderId,
        user_id: transaction.userId,
        amount: transaction.amount,
        status: transaction.status || 'pending',
        payment_method: transaction.paymentMethod
      });
      console.log(`✓ Migrated transaction: ${transaction.id}`);
    } catch (error) {
      console.error(`✗ Failed to migrate transaction ${transaction.id}:`, error);
    }
  }
}

// Main migration function
async function runMigration() {
  console.log('🚀 Starting data migration from JSON files to SQLite database...\n');

  try {
    // Initialize database connection
    const db = getDatabase();
    console.log('✓ Database connection established\n');

    // Run migration in a transaction
    await withTransaction(async () => {
      // Clear existing data (optional - comment out if you want to preserve existing data)
      console.log('Clearing existing data...');
      db.exec('DELETE FROM transactions');
      db.exec('DELETE FROM order_items');
      db.exec('DELETE FROM order_shipping_addresses');
      db.exec('DELETE FROM orders');
      db.exec('DELETE FROM reviews');
      db.exec('DELETE FROM products');
      db.exec('DELETE FROM user_addresses');
      db.exec('DELETE FROM users');
      db.exec('DELETE FROM categories');
      console.log('✓ Existing data cleared\n');

      // Run migrations in order (respecting foreign key constraints)
      await migrateCategories();
      console.log('');

      await migrateUsers();
      console.log('');

      await migrateProducts();
      console.log('');

      await migrateReviews();
      console.log('');

      await migrateOrders();
      console.log('');

      await migrateTransactions();
      console.log('');
    });

    console.log('🎉 Migration completed successfully!');

    // Print summary
    const db = getDatabase();
    const stats = {
      categories: db.prepare('SELECT COUNT(*) as count FROM categories').get() as { count: number },
      users: db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number },
      products: db.prepare('SELECT COUNT(*) as count FROM products').get() as { count: number },
      reviews: db.prepare('SELECT COUNT(*) as count FROM reviews').get() as { count: number },
      orders: db.prepare('SELECT COUNT(*) as count FROM orders').get() as { count: number },
      transactions: db.prepare('SELECT COUNT(*) as count FROM transactions').get() as { count: number }
    };

    console.log('\n📊 Migration Summary:');
    console.log(`   Categories: ${stats.categories.count}`);
    console.log(`   Users: ${stats.users.count}`);
    console.log(`   Products: ${stats.products.count}`);
    console.log(`   Reviews: ${stats.reviews.count}`);
    console.log(`   Orders: ${stats.orders.count}`);
    console.log(`   Transactions: ${stats.transactions.count}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n✅ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration script failed:', error);
      process.exit(1);
    });
}
