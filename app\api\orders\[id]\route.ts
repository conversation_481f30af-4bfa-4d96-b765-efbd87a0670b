import { NextResponse } from 'next/server'
import { OrderModel } from '../../../../lib/database/models'

// Transform database order to API format
function transformOrder(dbOrder: any, items: any[] = [], shippingAddress: any = null) {
  return {
    id: dbOrder.id,
    userId: dbOrder.user_id,
    items: items.map(item => ({
      id: item.id,
      orderId: item.order_id,
      productId: item.product_id,
      product: {
        id: item.product_id,
        name: item.product_name,
        price: item.price,
        image: item.product_image
      },
      quantity: item.quantity,
      price: item.price
    })),
    status: dbOrder.status,
    totalAmount: dbOrder.total_amount,
    shippingAddress: shippingAddress ? {
      fullName: shippingAddress.full_name,
      phone: shippingAddress.phone,
      address: shippingAddress.address,
      city: shippingAddress.city,
      district: shippingAddress.district,
      ward: shippingAddress.ward
    } : null,
    paymentMethod: dbOrder.payment_method,
    createdAt: dbOrder.created_at
  }
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderDetails = OrderModel.getOrderWithDetails(id)

    if (!orderDetails) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    const transformedOrder = transformOrder(orderDetails, orderDetails.items, orderDetails.shippingAddress)
    return NextResponse.json(transformedOrder)
  } catch (error) {
    console.error('Error reading order:', error)
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: orderId } = await params

    // Check if order exists
    const order = OrderModel.getById(orderId)
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Check order status
    if (order.status !== 'pending') {
      return NextResponse.json(
        { error: 'Cannot delete order that is not in pending status' },
        { status: 400 }
      )
    }

    // Import transaction helper and related models
    const { OrderItemModel, OrderShippingAddressModel, withTransaction } = await import('../../../../lib/database/models')

    // Delete order and related data in a transaction
    await withTransaction(async () => {
      // Delete order items
      OrderItemModel.deleteByOrderId(orderId)

      // Delete shipping address
      OrderShippingAddressModel.deleteByOrderId(orderId)

      // Delete the order
      OrderModel.delete(orderId)
    })

    return NextResponse.json({ message: 'Order deleted successfully' })
  } catch (error) {
    console.error('Error deleting order:', error)
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    )
  }
}