import { NextResponse } from "next/server"
import { UserModel } from "../../../../lib/database/models"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { name, email, password } = body

    // Validate required fields
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: "Vui lòng điền đầy đủ thông tin." },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingUser = UserModel.getByEmail(email)
    if (existingUser) {
      return NextResponse.json(
        { error: "Email này đã được sử dụng." },
        { status: 400 }
      )
    }

    // Generate new user ID
    const existingUsers = UserModel.getAll()
    const lastId = existingUsers.length > 0
      ? Math.max(...existingUsers.map(u => {
          const match = u.id.match(/\d+$/)
          return match ? parseInt(match[0]) : 0
        }))
      : 0
    const newId = `user-${String(lastId + 1).padStart(3, "0")}`

    // Create new user
    const newUser = UserModel.create({
      id: newId,
      name,
      email,
      password,
      phone: "",
      role: "customer",
      status: "active"
    })

    // Return user info (without password)
    const { password: _, ...userWithoutPassword } = newUser
    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error("Registration error:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi đăng ký." },
      { status: 500 }
    )
  }
}