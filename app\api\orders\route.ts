import { NextResponse } from 'next/server'
import { OrderModel } from '../../../lib/database/models'

// Transform database order to API format
function transformOrder(dbOrder: any, items: any[] = [], shippingAddress: any = null) {
  return {
    id: dbOrder.id,
    userId: dbOrder.user_id,
    items: items.map(item => ({
      id: item.id,
      orderId: item.order_id,
      productId: item.product_id,
      product: {
        id: item.product_id,
        name: item.product_name,
        price: item.price,
        image: item.product_image
      },
      quantity: item.quantity,
      price: item.price
    })),
    status: dbOrder.status,
    totalAmount: dbOrder.total_amount,
    shippingAddress: shippingAddress ? {
      fullName: shippingAddress.full_name,
      phone: shippingAddress.phone,
      address: shippingAddress.address,
      city: shippingAddress.city,
      district: shippingAddress.district,
      ward: shippingAddress.ward
    } : null,
    paymentMethod: dbOrder.payment_method,
    createdAt: dbOrder.created_at
  }
}

export async function GET(request: Request) {
  try {
    // Get userId from query parameters
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get user orders from database
    const orders = OrderModel.getByUserId(userId)

    // Transform orders with their details
    const transformedOrders = orders.map(order => {
      const orderDetails = OrderModel.getOrderWithDetails(order.id)
      return transformOrder(order, orderDetails?.items || [], orderDetails?.shippingAddress)
    })

    return NextResponse.json(transformedOrders)
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json()

    // Validate required fields
    if (!newOrder.userId || !newOrder.items || !newOrder.totalAmount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Import the required models and transaction helper
    const { OrderItemModel, OrderShippingAddressModel, withTransaction } = await import('../../../lib/database/models')

    // Create order in a transaction
    const createdOrder = await withTransaction(async () => {
      // Create the order
      const order = OrderModel.create({
        id: newOrder.id,
        user_id: newOrder.userId,
        status: newOrder.status || 'pending',
        total_amount: newOrder.totalAmount,
        payment_method: newOrder.paymentMethod
      })

      // Create shipping address if provided
      if (newOrder.shippingAddress) {
        OrderShippingAddressModel.create({
          order_id: order.id,
          full_name: newOrder.shippingAddress.fullName,
          phone: newOrder.shippingAddress.phone,
          address: newOrder.shippingAddress.address,
          city: newOrder.shippingAddress.city,
          district: newOrder.shippingAddress.district,
          ward: newOrder.shippingAddress.ward
        })
      }

      // Create order items
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          OrderItemModel.create({
            id: item.id,
            order_id: order.id,
            product_id: item.productId,
            quantity: item.quantity,
            price: item.price,
            product_name: item.product?.name || 'Unknown Product',
            product_image: item.product?.image || null
          })
        }
      }

      return order
    })

    // Get the complete order details
    const orderDetails = OrderModel.getOrderWithDetails(createdOrder.id)
    const transformedOrder = transformOrder(createdOrder, orderDetails?.items || [], orderDetails?.shippingAddress)

    return NextResponse.json(transformedOrder, { status: 201 })
  } catch (error) {
    console.error('Error adding order:', error)
    return NextResponse.json(
      { error: 'Failed to add order' },
      { status: 500 }
    )
  }
}