"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { ChevronRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import DashboardLayout from "@/app/dashboard/layout"

interface OrderItem {
  id: string
  productId: string
  product: {
    id: string
    name: string
    price: number
    image: string
    images: string[]
  }
  quantity: number
  price: number
}

interface Order {
  id: string
  userId: string
  items: OrderItem[]
  status: string
  totalAmount: number
  shippingAddress: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
  paymentMethod: string
  createdAt: string
}

interface OrderPageProps {
  params: {
    id: string
  }
}

export default function OrderPage({ params }: OrderPageProps) {
  const { id } = params
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      setUser(JSON.parse(userData))
    }
  }, [])

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        const response = await fetch(`/api/orders/${id}`)
        if (!response.ok) {
          throw new Error('Failed to fetch order')
        }
        const data = await response.json()
        setOrder(data)
      } catch (error) {
        console.error('Error fetching order:', error)
        toast({
          title: "Lỗi",
          description: "Không thể tải thông tin đơn hàng. Vui lòng thử lại sau.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchOrder()
  }, [id])

  const handleCancelOrder = async () => {
    try {
      const response = await fetch(`/api/orders/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to cancel order')
      }

      toast({
        title: "Thành công",
        description: "Đơn hàng đã được hủy thành công.",
      })

      // Chuyển hướng về trang danh sách đơn hàng sau 1 giây
      setTimeout(() => {
        window.location.href = '/orders'
      }, 1000)
    } catch (error: any) {
      console.error('Error canceling order:', error)
    toast({
        title: "Lỗi",
        description: error.message || "Không thể hủy đơn hàng. Vui lòng thử lại sau.",
        variant: "destructive",
    })
  }
  }

  if (loading) {
    return (
      <DashboardLayout user={user || undefined}>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (!order) {
  return (
      <DashboardLayout user={user || undefined}>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Không tìm thấy đơn hàng</h2>
            <p className="text-muted-foreground mb-4">
              Đơn hàng bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
            </p>
            <Button asChild>
              <Link href="/orders">Quay lại danh sách đơn hàng</Link>
              </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const canCancel = order.status === "pending" || order.status === "processing"
  const orderDate = new Date(order.createdAt).toLocaleDateString('vi-VN')

  return (
    <DashboardLayout user={user || undefined}>
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
            <Link href="/" className="hover:text-foreground">
              Trang chủ
            </Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/profile" className="hover:text-foreground">
              Tài khoản
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground">Đơn hàng {order.id}</span>
          </div>

          <div className="mb-8 flex flex-wrap items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">Chi tiết đơn hàng</h1>
              <p className="text-muted-foreground mt-2">
            Mã đơn hàng: {order.id} | Ngày đặt: {orderDate}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <span
                className={`inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${
              order.status === "delivered"
                    ? "bg-green-100 text-green-800"
                : order.status === "shipping"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-yellow-100 text-yellow-800"
                }`}
              >
            {order.status === "delivered" ? "Đã giao hàng" :
             order.status === "shipping" ? "Đang giao hàng" :
             order.status === "processing" ? "Đang xử lý" : "Chờ xác nhận"}
              </span>
              {canCancel && (
                <Button variant="outline" onClick={handleCancelOrder}>
                  Hủy đơn hàng
                </Button>
              )}
            </div>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <div className="md:col-span-2">
              <div className="rounded-lg border">
                <div className="p-6">
                  <h2 className="text-lg font-semibold">Sản phẩm</h2>
                </div>
                <Separator />
                {order.items.map((item) => (
                  <div key={item.id} className="p-6">
                    <div className="flex gap-4">
                      <div className="h-20 w-20 flex-shrink-0 overflow-hidden rounded-md border">
                        <img
                      src={item.product.image}
                      alt={item.product.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="flex flex-1 flex-col">
                        <div className="flex justify-between">
                      <h3 className="font-medium">{item.product.name}</h3>
                      <p className="font-medium">{formatCurrency(item.price)}</p>
                        </div>
                        <div className="mt-1 flex items-center justify-between">
                          <p className="text-sm text-muted-foreground">
                        {formatCurrency(item.product.price)} x {item.quantity}
                          </p>
                        </div>
                      </div>
                    </div>
                    <Separator className="mt-6" />
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-6">
              <div className="rounded-lg border">
                <div className="p-6">
                  <h2 className="text-lg font-semibold">Tóm tắt đơn hàng</h2>
                  <div className="mt-6 space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tạm tính</span>
                  <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Phí vận chuyển</span>
                  <span>Miễn phí</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between font-medium">
                      <span>Tổng cộng</span>
                  <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="rounded-lg border">
                <div className="p-6">
                  <h2 className="text-lg font-semibold">Thông tin giao hàng</h2>
                  <div className="mt-4 space-y-2">
                    <p>
                      <span className="font-medium">Người nhận:</span> {order.shippingAddress.fullName}
                    </p>
                    <p>
                      <span className="font-medium">Số điện thoại:</span> {order.shippingAddress.phone}
                    </p>
                    <p>
                      <span className="font-medium">Địa chỉ:</span> {order.shippingAddress.address},{" "}
                      {order.shippingAddress.ward}, {order.shippingAddress.district}, {order.shippingAddress.city}
                    </p>
                  </div>
                </div>
              </div>
              <div className="rounded-lg border">
                <div className="p-6">
                  <h2 className="text-lg font-semibold">Phương thức thanh toán</h2>
              <p className="mt-4">
                {order.paymentMethod === "credit_card" ? "Thẻ tín dụng" :
                 order.paymentMethod === "bank_transfer" ? "Chuyển khoản ngân hàng" :
                 order.paymentMethod === "momo" ? "Ví MoMo" :
                 "Thanh toán khi nhận hàng (COD)"}
              </p>
            </div>
          </div>
        </div>
        </div>
    </DashboardLayout>
  )
}
