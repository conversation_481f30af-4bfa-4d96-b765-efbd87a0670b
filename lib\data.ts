import type { Product, Category } from "./types"

// Categories
const categories: Category[] = [
  { id: "1", name: "<PERSON><PERSON><PERSON><PERSON> thoạ<PERSON>", slug: "dien-thoai" },
  { id: "2", name: "<PERSON><PERSON><PERSON>", slug: "laptop" },
  { id: "3", name: "<PERSON><PERSON><PERSON> bảng", slug: "may-tinh-bang" },
  { id: "4", name: "<PERSON><PERSON> kiện", slug: "phu-kien" },
  { id: "5", name: "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", slug: "khuyen-mai" },
]

// Generate mock products
const generateMockProducts = (): Product[] => {
  const products: Product[] = []

  // Phones
  const phoneData = [
    {
      name: "iPhone 15 Pro Max",
      image: "/images/products/iphone-15-pro-max.jpg",
      price: 34990000,
      originalPrice: 36990000,
      discount: 5,
    },
    {
      name: "iPhone 15 Pro",
      image: "/images/products/iphone-15-pro.jpg",
      price: 28990000,
      originalPrice: 29990000,
      discount: 3,
    },
    {
      name: "iPhone 15",
      image: "/images/products/iphone-15.jpg",
      price: 22990000,
      originalPrice: 24990000,
      discount: 8,
    },
    {
      name: "Samsung Galaxy S24 Ultra",
      image: "/images/products/samsung-s24-ultra.jpg",
      price: 31990000,
      originalPrice: 33990000,
      discount: 6,
    },
    {
      name: "Samsung Galaxy S24+",
      image: "/images/products/samsung-s24-plus.jpg",
      price: 25990000,
      originalPrice: 27990000,
      discount: 7,
    },
    {
      name: "Samsung Galaxy S24",
      image: "/images/products/samsung-s24.jpg",
      price: 20990000,
      originalPrice: 22990000,
      discount: 9,
    },
    {
      name: "Xiaomi 14 Ultra",
      image: "/images/products/xiaomi-14-ultra.jpg",
      price: 23990000,
      originalPrice: 25990000,
      discount: 8,
    },
    {
      name: "Xiaomi 14 Pro",
      image: "/images/products/xiaomi-14-pro.jpg",
      price: 19990000,
      originalPrice: 21990000,
      discount: 9,
    },
    {
      name: "Google Pixel 8 Pro",
      image: "/images/products/pixel-8-pro.jpg",
      price: 22990000,
      originalPrice: 24990000,
      discount: 8,
    },
    {
      name: "Google Pixel 8",
      image: "/images/products/pixel-8.jpg",
      price: 17990000,
      originalPrice: 19990000,
      discount: 10,
    },
    {
      name: "OPPO Find X7 Ultra",
      image: "/images/products/oppo-find-x7-ultra.jpg",
      price: 24990000,
      originalPrice: 26990000,
      discount: 7,
    },
    {
      name: "OPPO Find X7",
      image: "/images/products/oppo-find-x7.jpg",
      price: 19990000,
      originalPrice: 21990000,
      discount: 9,
    },
    {
      name: "Vivo X100 Pro",
      image: "/images/products/vivo-x100-pro.jpg",
      price: 22990000,
      originalPrice: 24990000,
      discount: 8,
    },
    {
      name: "Vivo X100",
      image: "/images/products/vivo-x100.jpg",
      price: 18990000,
      originalPrice: 20990000,
      discount: 10,
    },
    {
      name: "Realme GT 5 Pro",
      image: "/images/products/realme-gt-5-pro.jpg",
      price: 16990000,
      originalPrice: 18990000,
      discount: 11,
    },
  ]

  phoneData.forEach((phone, index) => {
    products.push({
      id: `phone-${index + 1}`,
      name: phone.name,
      description: `${phone.name} - Điện thoại cao cấp với hiệu năng mạnh mẽ, camera chất lượng và thời lượng pin dài.`,
      price: phone.price,
      originalPrice: phone.originalPrice,
      image: phone.image,
      category: categories[0],
      stock: Math.floor(Math.random() * 50) + 1,
      rating: Math.floor(Math.random() * 5) + 1,
      reviewCount: Math.floor(Math.random() * 500) + 10,
      soldCount: Math.floor(Math.random() * 1000) + 50,
      featured: index < 4,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      discount: phone.discount,
    })
  })

  // Laptops
  const laptopData = [
    {
      name: 'MacBook Pro 16"',
      image: "/images/products/macbook-pro-16.jpg",
      price: 59990000,
      originalPrice: 62990000,
      discount: 5,
    },
    {
      name: 'MacBook Pro 14"',
      image: "/images/products/macbook-pro-14.jpg",
      price: 49990000,
      originalPrice: 52990000,
      discount: 6,
    },
    {
      name: 'MacBook Air 13"',
      image: "/images/products/macbook-air-13.jpg",
      price: 29990000,
      originalPrice: 31990000,
      discount: 6,
    },
    {
      name: "Dell XPS 15",
      image: "/images/products/dell-xps-15.jpg",
      price: 45990000,
      originalPrice: 47990000,
      discount: 4,
    },
    {
      name: "Dell XPS 13",
      image: "/images/products/dell-xps-13.jpg",
      price: 35990000,
      originalPrice: 37990000,
      discount: 5,
    },
    {
      name: "HP Spectre x360",
      image: "/images/products/hp-spectre-x360.jpg",
      price: 39990000,
      originalPrice: 41990000,
      discount: 5,
    },
    {
      name: "HP Envy 13",
      image: "/images/products/hp-envy-13.jpg",
      price: 29990000,
      originalPrice: 31990000,
      discount: 6,
    },
    {
      name: "Lenovo ThinkPad X1 Carbon",
      image: "/images/products/lenovo-thinkpad-x1.jpg",
      price: 42990000,
      originalPrice: 44990000,
      discount: 4,
    },
    {
      name: "Lenovo Yoga 9i",
      image: "/images/products/lenovo-yoga-9i.jpg",
      price: 37990000,
      originalPrice: 39990000,
      discount: 5,
    },
    {
      name: "ASUS ROG Zephyrus G14",
      image: "/images/products/asus-rog-zephyrus-g14.jpg",
      price: 39990000,
      originalPrice: 41990000,
      discount: 5,
    },
    {
      name: "ASUS ZenBook 14",
      image: "/images/products/asus-zenbook-14.jpg",
      price: 27990000,
      originalPrice: 29990000,
      discount: 7,
    },
    {
      name: "Acer Swift 5",
      image: "/images/products/acer-swift-5.jpg",
      price: 25990000,
      originalPrice: 27990000,
      discount: 7,
    },
    {
      name: "Acer Predator Helios 300",
      image: "/images/products/acer-predator-helios-300.jpg",
      price: 35990000,
      originalPrice: 37990000,
      discount: 5,
    },
    {
      name: "MSI GS66 Stealth",
      image: "/images/products/msi-gs66-stealth.jpg",
      price: 45990000,
      originalPrice: 47990000,
      discount: 4,
    },
    {
      name: "Microsoft Surface Laptop 5",
      image: "/images/products/surface-laptop-5.jpg",
      price: 32990000,
      originalPrice: 34990000,
      discount: 6,
    },
  ]

  laptopData.forEach((laptop, index) => {
    products.push({
      id: `laptop-${index + 1}`,
      name: laptop.name,
      description: `${laptop.name} - Laptop hiệu năng cao với thiết kế sang trọng, màn hình sắc nét và thời lượng pin dài.`,
      price: laptop.price,
      originalPrice: laptop.originalPrice,
      image: laptop.image,
      category: categories[1],
      stock: Math.floor(Math.random() * 30) + 1,
      rating: Math.floor(Math.random() * 5) + 1,
      reviewCount: Math.floor(Math.random() * 300) + 5,
      soldCount: Math.floor(Math.random() * 500) + 20,
      featured: index < 4,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      discount: laptop.discount,
    })
  })

  // Tablets
  const tabletData = [
    {
      name: 'iPad Pro 12.9"',
      image: "/images/products/ipad-pro-12.jpg",
      price: 32990000,
      originalPrice: 34990000,
      discount: 6,
    },
    {
      name: 'iPad Pro 11"',
      image: "/images/products/ipad-pro-11.jpg",
      price: 25990000,
      originalPrice: 27990000,
      discount: 7,
    },
    {
      name: "iPad Air",
      image: "/images/products/ipad-air.jpg",
      price: 18990000,
      originalPrice: 19990000,
      discount: 5,
    },
    {
      name: "iPad mini",
      image: "/images/products/ipad-mini.jpg",
      price: 14990000,
      originalPrice: 15990000,
      discount: 6,
    },
    {
      name: "Samsung Galaxy Tab S9 Ultra",
      image: "/images/products/samsung-tab-s9-ultra.jpg",
      price: 29990000,
      originalPrice: 31990000,
      discount: 6,
    },
    {
      name: "Samsung Galaxy Tab S9+",
      image: "/images/products/samsung-tab-s9-plus.jpg",
      price: 24990000,
      originalPrice: 26990000,
      discount: 7,
    },
    {
      name: "Samsung Galaxy Tab S9",
      image: "/images/products/samsung-tab-s9.jpg",
      price: 19990000,
      originalPrice: 21990000,
      discount: 9,
    },
    {
      name: "Xiaomi Pad 6 Pro",
      image: "/images/products/xiaomi-pad-6-pro.jpg",
      price: 12990000,
      originalPrice: 13990000,
      discount: 7,
    },
    {
      name: "Xiaomi Pad 6",
      image: "/images/products/xiaomi-pad-6.jpg",
      price: 8990000,
      originalPrice: 9990000,
      discount: 10,
    },
    {
      name: "Lenovo Tab P12 Pro",
      image: "/images/products/lenovo-tab-p12-pro.jpg",
      price: 16990000,
      originalPrice: 18990000,
      discount: 11,
    },
  ]

  tabletData.forEach((tablet, index) => {
    products.push({
      id: `tablet-${index + 1}`,
      name: tablet.name,
      description: `${tablet.name} - Máy tính bảng mạnh mẽ với màn hình lớn, hiệu năng cao và thời lượng pin dài.`,
      price: tablet.price,
      originalPrice: tablet.originalPrice,
      image: tablet.image,
      category: categories[2],
      stock: Math.floor(Math.random() * 40) + 1,
      rating: Math.floor(Math.random() * 5) + 1,
      reviewCount: Math.floor(Math.random() * 200) + 5,
      soldCount: Math.floor(Math.random() * 400) + 10,
      featured: index < 2,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      discount: tablet.discount,
    })
  })

  // Accessories
  const accessoryData = [
    {
      name: "Apple AirPods Pro",
      image: "/images/products/airpods-pro.jpg",
      price: 6990000,
      originalPrice: 7990000,
      discount: 13,
    },
    {
      name: "Apple AirPods Max",
      image: "/images/products/airpods-max.jpg",
      price: 12990000,
      originalPrice: 13990000,
      discount: 7,
    },
    {
      name: "Samsung Galaxy Buds Pro",
      image: "/images/products/galaxy-buds-pro.jpg",
      price: 4990000,
      originalPrice: 5490000,
      discount: 9,
    },
    {
      name: "Sony WH-1000XM5",
      image: "/images/products/sony-wh-1000xm5.jpg",
      price: 8990000,
      originalPrice: 9990000,
      discount: 10,
    },
    {
      name: "Bose QuietComfort 45",
      image: "/images/products/bose-qc45.jpg",
      price: 7990000,
      originalPrice: 8990000,
      discount: 11,
    },
    {
      name: "Apple Watch Series 9",
      image: "/images/products/apple-watch-series-9.jpg",
      price: 11990000,
      originalPrice: 12990000,
      discount: 8,
    },
    {
      name: "Samsung Galaxy Watch 6",
      image: "/images/products/galaxy-watch-6.jpg",
      price: 7990000,
      originalPrice: 8990000,
      discount: 11,
    },
    {
      name: "Garmin Fenix 7",
      image: "/images/products/garmin-fenix-7.jpg",
      price: 18990000,
      originalPrice: 19990000,
      discount: 5,
    },
    {
      name: "Anker PowerCore 26800",
      image: "/images/products/anker-powercore.jpg",
      price: 1490000,
      originalPrice: 1790000,
      discount: 17,
    },
    {
      name: "Belkin Wireless Charger",
      image: "/images/products/belkin-wireless-charger.jpg",
      price: 990000,
      originalPrice: 1290000,
      discount: 23,
    },
  ]

  accessoryData.forEach((accessory, index) => {
    products.push({
      id: `accessory-${index + 1}`,
      name: accessory.name,
      description: `${accessory.name} - Phụ kiện chất lượng cao với thiết kế đẹp và tính năng hữu ích.`,
      price: accessory.price,
      originalPrice: accessory.originalPrice,
      image: accessory.image,
      category: categories[3],
      stock: Math.floor(Math.random() * 100) + 10,
      rating: Math.floor(Math.random() * 5) + 1,
      reviewCount: Math.floor(Math.random() * 100) + 5,
      soldCount: Math.floor(Math.random() * 300) + 20,
      featured: index < 2,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      discount: accessory.discount,
    })
  })

  // Special promotions - additional products with higher discounts
  const specialPromotions = [
    {
      id: "promo-1",
      name: "Samsung Galaxy S23 Ultra",
      description:
        "Samsung Galaxy S23 Ultra - Điện thoại cao cấp với hiệu năng mạnh mẽ, camera chất lượng và thời lượng pin dài.",
      price: 23990000,
      originalPrice: 29990000,
      image: "/images/products/promotions/samsung-s23-ultra.png",
      category: categories[0],
      stock: 15,
      rating: 5,
      reviewCount: 320,
      soldCount: 850,
      featured: true,
      createdAt: new Date(Date.now() - 5000000000),
      discount: 20,
    },
    {
      id: "promo-2",
      name: "iPhone 14 Pro Max",
      description:
        "iPhone 14 Pro Max - Điện thoại cao cấp với hiệu năng mạnh mẽ, camera chất lượng và thời lượng pin dài.",
      price: 25990000,
      originalPrice: 31990000,
      image: "/images/products/promotions/iphone-14-pro-max.png",
      category: categories[0],
      stock: 10,
      rating: 5,
      reviewCount: 450,
      soldCount: 920,
      featured: true,
      createdAt: new Date(Date.now() - 6000000000),
      discount: 19,
    },
    {
      id: "promo-3",
      name: "MacBook Air M2",
      description:
        "MacBook Air M2 - Laptop hiệu năng cao với thiết kế sang trọng, màn hình sắc nét và thời lượng pin dài.",
      price: 24990000,
      originalPrice: 29990000,
      image: "/images/products/promotions/macbook-air-m2.png",
      category: categories[1],
      stock: 8,
      rating: 5,
      reviewCount: 280,
      soldCount: 520,
      featured: true,
      createdAt: new Date(Date.now() - 7000000000),
      discount: 17,
    },
    {
      id: "promo-4",
      name: "iPad 10th Gen",
      description: "iPad 10th Gen - Máy tính bảng mạnh mẽ với màn hình lớn, hiệu năng cao và thời lượng pin dài.",
      price: 9990000,
      originalPrice: 11990000,
      image: "/images/products/promotions/ipad-10th-gen.png",
      category: categories[2],
      stock: 20,
      rating: 4,
      reviewCount: 180,
      soldCount: 350,
      featured: true,
      createdAt: new Date(Date.now() - 8000000000),
      discount: 17,
    },
    {
      id: "promo-5",
      name: "Samsung Galaxy Buds 2 Pro",
      description:
        "Samsung Galaxy Buds 2 Pro - Tai nghe không dây chất lượng cao với âm thanh sống động và khả năng chống ồn tuyệt vời.",
      price: 3990000,
      originalPrice: 4990000,
      image: "/images/products/promotions/galaxy-buds-2-pro.png",
      category: categories[3],
      stock: 30,
      rating: 4,
      reviewCount: 150,
      soldCount: 280,
      featured: true,
      createdAt: new Date(Date.now() - 9000000000),
      discount: 20,
    },
  ]

  // Add special promotions to products array
  products.push(...specialPromotions)

  return products
}

const products = generateMockProducts()

// Get products with optional filters
export function getProducts({
  category,
  featured,
  limit,
  sort,
  minDiscount,
}: {
  category?: string
  featured?: boolean
  limit?: number
  sort?: "price_asc" | "price_desc" | "newest" | "popular" | "discount"
  minDiscount?: number
} = {}) {
  let filteredProducts = [...products]

  if (category) {
    // Special case for "khuyen-mai" category
    if (category === "khuyen-mai") {
      // For promotions, get products with higher discounts
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.originalPrice && ((product.originalPrice - product.price) / product.originalPrice) * 100 >= 10,
      )
    } else {
      filteredProducts = filteredProducts.filter((product) => product.category.slug === category)
    }
  }

  if (featured !== undefined) {
    filteredProducts = filteredProducts.filter((product) => product.featured === featured)
  }

  if (minDiscount !== undefined) {
    filteredProducts = filteredProducts.filter(
      (product) =>
        product.originalPrice && ((product.originalPrice - product.price) / product.originalPrice) * 100 >= minDiscount,
    )
  }

  // Sort products
  if (sort) {
    switch (sort) {
      case "price_asc":
        filteredProducts.sort((a, b) => a.price - b.price)
        break
      case "price_desc":
        filteredProducts.sort((a, b) => b.price - a.price)
        break
      case "newest":
        filteredProducts.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        break
      case "popular":
        filteredProducts.sort((a, b) => b.soldCount - a.soldCount)
        break
      case "discount":
        filteredProducts.sort((a, b) => {
          const discountA = a.originalPrice ? ((a.originalPrice - a.price) / a.originalPrice) * 100 : 0
          const discountB = b.originalPrice ? ((b.originalPrice - b.price) / b.originalPrice) * 100 : 0
          return discountB - discountA
        })
        break
    }
  }

  if (limit) {
    filteredProducts = filteredProducts.slice(0, limit)
  }

  return filteredProducts
}

// Get promotional products (with discounts)
export function getPromotionalProducts(limit?: number) {
  // Get products with at least 10% discount, sorted by discount percentage
  return getProducts({ minDiscount: 10, sort: "discount", limit })
}

// Get product by ID
export function getProductById(id: string) {
  return products.find((product) => product.id === id)
}

// Get related products
export function getRelatedProducts(productId: string, limit = 4) {
  const product = getProductById(productId)
  if (!product) return []

  return products.filter((p) => p.category.id === product.category.id && p.id !== productId).slice(0, limit)
}

// Get categories
export function getCategories() {
  return categories
}
