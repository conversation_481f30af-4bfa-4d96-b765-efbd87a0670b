"use client"

import { ReactNode, useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Users,
  Package,
  FolderTree,
  CreditCard,
  Star,
  BarChart3,
  Menu,
  X,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

interface AdminLayoutProps {
  children: ReactNode
}

const navigation = [
  {
    name: "Quản lý tài khoản",
    href: "/admin/users",
    icon: Users,
  },
  {
    name: "Quản lý sản phẩm",
    href: "/admin/products",
    icon: Package,
  },
  {
    name: "Quản lý danh mục",
    href: "/admin/categories",
    icon: FolderTree,
  },
  {
    name: "Quản lý giao dịch",
    href: "/admin/transactions",
    icon: CreditCard,
  },
  {
    name: "Quản lý đánh giá",
    href: "/admin/reviews",
    icon: Star,
  },
  {
    name: "<PERSON>áo c<PERSON>o hệ thống",
    href: "/admin/reports",
    icon: BarChart3,
  },
]

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname()
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      const parsedUser = JSON.parse(userData)
      if (parsedUser.role !== "admin") {
        window.location.href = "/"
      }
      setUser(parsedUser)
    } else {
      window.location.href = "/login"
    }
  }, [])

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar toggle */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-64 bg-background border-r transform transition-transform duration-200 ease-in-out lg:translate-x-0",
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-6">
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          </div>
          <Separator />
          <nav className="flex-1 p-4 space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  {item.name}
                </Link>
              )
            })}
          </nav>
          <div className="p-4">
            <Separator className="mb-4" />
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-muted-foreground">{user.email}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  localStorage.removeItem("user")
                  window.location.href = "/"
                }}
              >
                Đăng xuất
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        <main className="p-8">{children}</main>
      </div>
    </div>
  )
} 