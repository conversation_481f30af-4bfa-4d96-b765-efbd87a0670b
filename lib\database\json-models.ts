import fs from 'fs';
import path from 'path';

// JSON-based models as fallback when SQLite is not available
const DATA_DIR = path.join(process.cwd(), 'data');

// Helper function to read JSON file
function readJsonFile<T>(filename: string): T[] {
  try {
    const filePath = path.join(DATA_DIR, filename);
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
}

// Helper function to write JSON file
function writeJsonFile<T>(filename: string, data: T[]): void {
  try {
    const filePath = path.join(DATA_DIR, filename);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error(`Error writing ${filename}:`, error);
  }
}

// Transform JSON data to database format
function transformUser(user: any) {
  return {
    id: user.id,
    name: user.name,
    email: user.email,
    password: user.password,
    phone: user.phone || '',
    role: user.role || 'customer',
    status: user.status || 'active',
    created_at: user.createdAt || new Date().toISOString()
  };
}

function transformProduct(product: any) {
  return {
    id: product.id,
    name: product.name,
    description: product.description,
    price: product.price,
    original_price: product.originalPrice,
    image: product.image,
    images: JSON.stringify(product.images || []),
    category_id: product.category?.id || product.category?.slug || 'unknown',
    stock: product.stock || 0,
    rating: product.rating || 0,
    review_count: product.reviewCount || 0,
    sold_count: product.soldCount || 0,
    featured: Boolean(product.featured),
    is_promotional: Boolean(product.isPromotional),
    promotion_ends: product.promotionEnds || null,
    created_at: product.createdAt || new Date().toISOString()
  };
}

function transformCategory(category: any) {
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description,
    image: category.image,
    status: category.status || 'active',
    created_at: category.createdAt || new Date().toISOString()
  };
}

// JSON-based User Model
export class JsonUserModel {
  static getAll() {
    const users = readJsonFile<any>('users.json');
    return users.map(transformUser);
  }

  static getById(id: string) {
    const users = readJsonFile<any>('users.json');
    const user = users.find((u: any) => u.id === id);
    return user ? transformUser(user) : null;
  }

  static getByEmail(email: string) {
    const users = readJsonFile<any>('users.json');
    const user = users.find((u: any) => u.email === email);
    return user ? transformUser(user) : null;
  }

  static create(data: any) {
    const users = readJsonFile<any>('users.json');
    const newUser = {
      id: data.id,
      name: data.name,
      email: data.email,
      password: data.password,
      phone: data.phone || '',
      role: data.role || 'customer',
      status: data.status || 'active',
      createdAt: new Date().toISOString()
    };
    users.push(newUser);
    writeJsonFile('users.json', users);
    return transformUser(newUser);
  }

  static update(id: string, data: any) {
    const users = readJsonFile<any>('users.json');
    const index = users.findIndex((u: any) => u.id === id);
    if (index === -1) return null;
    
    users[index] = { ...users[index], ...data };
    writeJsonFile('users.json', users);
    return transformUser(users[index]);
  }

  static delete(id: string) {
    const users = readJsonFile<any>('users.json');
    const index = users.findIndex((u: any) => u.id === id);
    if (index === -1) return false;
    
    users.splice(index, 1);
    writeJsonFile('users.json', users);
    return true;
  }
}

// JSON-based Product Model
export class JsonProductModel {
  static getAll(options: any = {}) {
    const products = readJsonFile<any>('products.json');
    const promotionalProducts = readJsonFile<any>('promotionalProducts.json');
    
    let allProducts = [
      ...products.map((p: any) => ({ ...p, isPromotional: false })),
      ...promotionalProducts.map((p: any) => ({ ...p, isPromotional: true }))
    ];

    // Apply filters
    if (options.featured !== undefined) {
      allProducts = allProducts.filter((p: any) => Boolean(p.featured) === options.featured);
    }
    
    if (options.isPromotional !== undefined) {
      allProducts = allProducts.filter((p: any) => Boolean(p.isPromotional) === options.isPromotional);
    }
    
    if (options.categoryId) {
      allProducts = allProducts.filter((p: any) => 
        p.category?.id === options.categoryId || p.category?.slug === options.categoryId
      );
    }

    // Apply sorting
    if (options.orderBy) {
      allProducts.sort((a: any, b: any) => {
        const aVal = a[options.orderBy] || 0;
        const bVal = b[options.orderBy] || 0;
        return options.orderDirection === 'DESC' ? bVal - aVal : aVal - bVal;
      });
    }

    // Apply limit
    if (options.limit) {
      allProducts = allProducts.slice(0, options.limit);
    }

    return allProducts.map(transformProduct);
  }

  static getById(id: string) {
    const products = readJsonFile<any>('products.json');
    const promotionalProducts = readJsonFile<any>('promotionalProducts.json');
    
    let product = products.find((p: any) => p.id === id);
    if (!product) {
      product = promotionalProducts.find((p: any) => p.id === id);
      if (product) {
        product.isPromotional = true;
      }
    }
    
    return product ? transformProduct(product) : null;
  }

  static getFeatured(limit?: number) {
    return this.getAll({ featured: true, limit });
  }

  static getPromotional(limit?: number) {
    return this.getAll({ isPromotional: true, limit });
  }

  static getByCategoryId(categoryId: string, limit?: number) {
    return this.getAll({ categoryId, limit });
  }

  static search(query: string, limit?: number) {
    const allProducts = this.getAll();
    const filtered = allProducts.filter((p: any) => 
      p.name.toLowerCase().includes(query.toLowerCase()) ||
      (p.description && p.description.toLowerCase().includes(query.toLowerCase()))
    );
    
    return limit ? filtered.slice(0, limit) : filtered;
  }

  static create(data: any) {
    // For simplicity, add to regular products
    const products = readJsonFile<any>('products.json');
    const newProduct = {
      id: data.id,
      name: data.name,
      description: data.description,
      price: data.price,
      originalPrice: data.original_price,
      image: data.image,
      images: data.images ? JSON.parse(data.images) : [],
      category: { id: data.category_id },
      stock: data.stock || 0,
      rating: data.rating || 0,
      reviewCount: data.review_count || 0,
      soldCount: data.sold_count || 0,
      featured: Boolean(data.featured),
      createdAt: new Date().toISOString()
    };
    
    products.push(newProduct);
    writeJsonFile('products.json', products);
    return transformProduct(newProduct);
  }

  static update(id: string, data: any) {
    // Try to update in regular products first
    const products = readJsonFile<any>('products.json');
    const index = products.findIndex((p: any) => p.id === id);
    
    if (index !== -1) {
      products[index] = { ...products[index], ...data };
      writeJsonFile('products.json', products);
      return transformProduct(products[index]);
    }
    
    // Try promotional products
    const promoProducts = readJsonFile<any>('promotionalProducts.json');
    const promoIndex = promoProducts.findIndex((p: any) => p.id === id);
    
    if (promoIndex !== -1) {
      promoProducts[promoIndex] = { ...promoProducts[promoIndex], ...data };
      writeJsonFile('promotionalProducts.json', promoProducts);
      return transformProduct(promoProducts[promoIndex]);
    }
    
    return null;
  }

  static delete(id: string) {
    // Try to delete from regular products first
    const products = readJsonFile<any>('products.json');
    const index = products.findIndex((p: any) => p.id === id);
    
    if (index !== -1) {
      products.splice(index, 1);
      writeJsonFile('products.json', products);
      return true;
    }
    
    // Try promotional products
    const promoProducts = readJsonFile<any>('promotionalProducts.json');
    const promoIndex = promoProducts.findIndex((p: any) => p.id === id);
    
    if (promoIndex !== -1) {
      promoProducts.splice(promoIndex, 1);
      writeJsonFile('promotionalProducts.json', promoProducts);
      return true;
    }
    
    return false;
  }
}

// JSON-based Category Model
export class JsonCategoryModel {
  static getAll() {
    const categories = readJsonFile<any>('categories.json');
    return categories.map(transformCategory);
  }

  static getById(id: string) {
    const categories = readJsonFile<any>('categories.json');
    const category = categories.find((c: any) => c.id === id);
    return category ? transformCategory(category) : null;
  }

  static getBySlug(slug: string) {
    const categories = readJsonFile<any>('categories.json');
    const category = categories.find((c: any) => c.slug === slug);
    return category ? transformCategory(category) : null;
  }

  static create(data: any) {
    const categories = readJsonFile<any>('categories.json');
    const newCategory = {
      id: data.id,
      name: data.name,
      slug: data.slug,
      description: data.description,
      image: data.image,
      status: data.status || 'active',
      createdAt: new Date().toISOString()
    };
    
    categories.push(newCategory);
    writeJsonFile('categories.json', categories);
    return transformCategory(newCategory);
  }

  static update(id: string, data: any) {
    const categories = readJsonFile<any>('categories.json');
    const index = categories.findIndex((c: any) => c.id === id);
    if (index === -1) return null;
    
    categories[index] = { ...categories[index], ...data };
    writeJsonFile('categories.json', categories);
    return transformCategory(categories[index]);
  }

  static delete(id: string) {
    const categories = readJsonFile<any>('categories.json');
    const index = categories.findIndex((c: any) => c.id === id);
    if (index === -1) return false;
    
    categories.splice(index, 1);
    writeJsonFile('categories.json', categories);
    return true;
  }
}
