import { NextResponse } from "next/server"
import { ProductModel } from "../../../../lib/database/models"

// Transform database product to API format
function transformProduct(dbProduct: any) {
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: dbProduct.category_id,
      name: dbProduct.category_name || dbProduct.category_id,
      slug: dbProduct.category_id
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: Boolean(dbProduct.featured),
    createdAt: dbProduct.created_at
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q")
    const limit = parseInt(searchParams.get("limit") || "10")

    if (!query || query.trim().length < 2) {
      return NextResponse.json([])
    }

    // Search products using the model's search method
    const products = ProductModel.search(query.trim(), limit)

    // Transform products to API format
    const transformedProducts = products.map(transformProduct)

    return NextResponse.json(transformedProducts)
  } catch (error) {
    console.error("Error searching products:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tìm kiếm sản phẩm." },
      { status: 500 }
    )
  }
}
