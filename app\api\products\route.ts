import { NextResponse } from "next/server"
import { ProductModel, CategoryModel } from "../../../lib/database/models"

// Transform database product to API format
function transformProduct(dbProduct: any) {
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: dbProduct.category_id,
      name: dbProduct.category_name || dbProduct.category_id,
      slug: dbProduct.category_id
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: Boolean(dbProduct.featured),
    createdAt: dbProduct.created_at
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const featured = searchParams.get("featured")
    const sort = searchParams.get("sort")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = parseInt(searchParams.get("offset") || "0")
    const categoryId = searchParams.get("category")
    const search = searchParams.get("search")
    const minPrice = searchParams.get("minPrice")
    const maxPrice = searchParams.get("maxPrice")
    const minRating = searchParams.get("minRating")
    const inStock = searchParams.get("inStock")

    // Build query options
    const queryOptions: any = {
      limit,
      offset,
      orderBy: 'created_at',
      orderDirection: 'DESC' as const
    }

    // Handle sorting
    switch (sort) {
      case "newest":
        queryOptions.orderBy = 'created_at'
        queryOptions.orderDirection = 'DESC'
        break
      case "oldest":
        queryOptions.orderBy = 'created_at'
        queryOptions.orderDirection = 'ASC'
        break
      case "price-asc":
        queryOptions.orderBy = 'price'
        queryOptions.orderDirection = 'ASC'
        break
      case "price-desc":
        queryOptions.orderBy = 'price'
        queryOptions.orderDirection = 'DESC'
        break
      case "rating":
        queryOptions.orderBy = 'rating'
        queryOptions.orderDirection = 'DESC'
        break
      case "popularity":
        queryOptions.orderBy = 'sold_count'
        queryOptions.orderDirection = 'DESC'
        break
      default:
        queryOptions.orderBy = 'created_at'
        queryOptions.orderDirection = 'DESC'
    }

    // Get products from database
    let products = ProductModel.getAll({
      featured: featured === "true" ? true : undefined,
      categoryId: categoryId || undefined,
      ...queryOptions
    })

    // Apply additional filters
    if (search) {
      const searchLower = search.toLowerCase()
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      )
    }

    if (minPrice) {
      products = products.filter(product => product.price >= parseInt(minPrice))
    }

    if (maxPrice) {
      products = products.filter(product => product.price <= parseInt(maxPrice))
    }

    if (minRating) {
      products = products.filter(product => product.rating >= parseFloat(minRating))
    }

    if (inStock === "true") {
      products = products.filter(product => product.stock > 0)
    }

    // Transform products to API format
    const transformedProducts = products.map(transformProduct)

    return NextResponse.json(transformedProducts)
  } catch (error) {
    console.error("Error fetching products:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm." },
      { status: 500 }
    )
  }
}