// Simple login test using built-in fetch (Node.js 18+)
console.log('🔐 Testing Admin Login API...\n');

async function testLogin() {
  try {
    console.log('Testing with admin credentials...');
    
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    console.log('Response Status:', response.status);
    
    const data = await response.json();
    console.log('Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ Admin login successful!');
      console.log(`Welcome ${data.name} (${data.role})`);
      return true;
    } else {
      console.log('\n❌ Admin login failed!');
      console.log('Error:', data.error);
      return false;
    }

  } catch (error) {
    console.error('❌ Login test failed:', error.message);
    return false;
  }
}

testLogin().then(success => {
  if (success) {
    console.log('\n🎉 Login functionality is working correctly!');
  } else {
    console.log('\n💥 Login functionality needs fixing!');
  }
});
