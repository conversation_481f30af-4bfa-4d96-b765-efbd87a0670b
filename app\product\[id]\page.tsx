"use client"

import { useState } from "react"
import Link from "next/link"
import { ChevronRight, Heart, Minus, Plus, ShoppingBag, Star, Truck } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import { getProductById, getRelatedProducts } from "@/lib/data"

interface ProductPageProps {
  params: {
    id: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const product = getProductById(params.id)
  const relatedProducts = getRelatedProducts(params.id)
  const [quantity, setQuantity] = useState(1)
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)

  if (!product) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <h1 className="text-2xl font-bold">Sản phẩm không tồn tại</h1>
        <Button asChild className="mt-4">
          <Link href="/">Quay lại trang chủ</Link>
        </Button>
      </div>
    )
  }

  const handleAddToCart = () => {
    toast({
      title: "Đã thêm vào giỏ hàng",
      description: `${quantity} ${product.name} đã được thêm vào giỏ hàng của bạn.`,
    })
  }

  const handleBuyNow = () => {
    toast({
      title: "Đang chuyển đến trang thanh toán",
      description: `Bạn đang mua ${quantity} ${product.name}.`,
    })
    // In a real app, this would redirect to the checkout page
  }

  const handleToggleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast({
      title: isWishlisted ? "Đã xóa khỏi danh sách yêu thích" : "Đã thêm vào danh sách yêu thích",
      description: isWishlisted 
        ? `${product.name} đã được xóa khỏi danh sách yêu thích của bạn.`
        : `${product.name} đã được thêm vào danh sách yêu thích của bạn.`,
    })
  }

  const incrementQuantity = () => {
    if (quantity < product.stock) {
      setQuantity(quantity + 1)
    }
  }

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1)
    }
  }

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) 
    : 0

  // Update the product images section
  const productImages = [
    product.image || "/placeholder.svg?height=600&width=600",
    product.image.replace('.jpg', '-2.jpg') || "/placeholder.svg?height=600&width=600&text=Image+2",
    product.image.replace('.jpg', '-3.jpg') || "/placeholder.svg?height=600&width=600&text=Image+3",
    product.image.replace('.jpg', '-4.jpg') || "/placeholder.svg?height=600&width=600&text=Image+4",
  ]

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>
          <div className="ml-auto flex items-center gap-4">
            <Link href="/cart">
              <Button variant="outline" size="icon">
                <ShoppingBag className="h-5 w-5" />
                <span className="sr-only">Giỏ hàng</span>
              </Button>
            </Link>
            <Link href="/login">
              <Button variant="outline">Đăng nhập</Button>
            </Link>
            <Link href="/register">
              <Button>Đăng ký</Button>
            </Link>
          </div>
        </div>
        <nav className="container flex h-12 items-center gap-6 text-sm">
          <Link href="/category/dien-thoai" className="font-medium transition-colors hover:text-primary">
            Điện thoại
          </Link>
          <Link href="/category/laptop" className="font-medium transition-colors hover:text-primary">
            Laptop
          </Link>
          <Link href="/category/may-tinh-bang" className="font-medium transition-colors hover:text-primary">
            Máy tính bảng
          </Link>
          <Link href="/category/phu-kien" className="font-medium transition-colors hover:text-primary">
            Phụ kiện
          </Link>
          <Link href="/category/khuyen-mai" className="font-medium transition-colors hover:text-primary">
            Khuyến mãi
          </Link>
        </nav>
      </header>
      <main className="flex-1">
        <div className="container py-6">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
            <Link href="/" className="hover:text-foreground">
              Trang chủ
            </Link>
            <ChevronRight className="h-4 w-4" />
            <Link href={`/category/${product.category.slug}`} className="hover:text-foreground">
              {product.category.name}
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground">{product.name}</span>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="overflow-hidden rounded-lg border">
                <img
                  src={productImages[selectedImage] || "/placeholder.svg"}
                  alt={product.name}
                  className="h-full w-full object-cover"
                />
              </div>
              <div className="grid grid-cols-4 gap-2">
                {productImages.map((image, index) => (
                  <button
                    key={index}
                    className={`overflow-hidden rounded-lg border ${
                      selectedImage === index ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => setSelectedImage(index)}
                  >
                    <img src={image || "/placeholder.svg"} alt={`${product.name} - Ảnh ${index + 1}`} className="h-full w-full object-cover" />
                  </button>
                ))}
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold">{product.name}</h1>
                <div className="mt-2 flex items-center gap-4">
                  <div className="flex items-center">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-5 w-5 ${i < product.rating ? "fill-primary text-primary" : "fill-muted text-muted"}`}
                      />
                    ))}
                    <span className="ml-2 text-sm text-muted-foreground">({product.reviewCount} đánh giá)</span>
                  </div>
                  <Separator orientation="vertical" className="h-5" />
                  <span className="text-sm text-muted-foreground">Đã bán: {product.soldCount}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-3xl font-bold">{formatCurrency(product.price)}</span>
                  {product.originalPrice && (
                    <>
                      <span className="text-lg text-muted-foreground line-through">
                        {formatCurrency(product.originalPrice)}
                      </span>
                      <Badge className="bg-red-500 hover:bg-red-600">-{discountPercentage}%</Badge>
                    </>
                  )}
                </div>
                {product.stock <= 5 && product.stock > 0 && (
                  <p className="text-sm text-red-500">Chỉ còn {product.stock} sản phẩm</p>
                )}
                {product.stock === 0 && (
                  <p className="text-sm text-red-500">Hết hàng</p>
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Truck className="h-5 w-5 text-muted-foreground" />
                  <span>Giao hàng miễn phí cho đơn hàng từ 500.000đ</span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm">Số lượng:</span>
                  <div className="flex items-center">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-r-none"
                      onClick={decrementQuantity}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-3 w-3" />
                      <span className="sr-only">Giảm</span>
                    </Button>
                    <div className="flex h-8 w-12 items-center justify-center border-y">
                      {quantity}
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-l-none"
                      onClick={incrementQuantity}
                      disabled={quantity >= product.stock}
                    >
                      <Plus className="h-3 w-3" />
                      <span className="sr-only">Tăng</span>
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-2 sm:flex-row">
                <Button 
                  className="flex-1" 
                  onClick={handleAddToCart}
                  disabled={product.stock === 0}
                >
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Thêm vào giỏ
                </Button>
                <Button 
                  variant="secondary" 
                  className="flex-1"
                  onClick={handleBuyNow}
                  disabled={product.stock === 0}
                >
                  Mua ngay
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className={isWishlisted ? 'bg-red-100' : ''}
                  onClick={handleToggleWishlist}
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-red-500 text-red-500' : ''}`} />
                  <span className="sr-only">Thêm vào yêu thích</span>
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-12">
            <Tabs defaultValue="description">
              <TabsList className="w-full justify-start">
                <TabsTrigger value="description">Mô tả sản phẩm</TabsTrigger>
                <TabsTrigger value="specifications">Thông số kỹ thuật</TabsTrigger>
                <TabsTrigger value="reviews">Đánh giá ({product.reviewCount})</TabsTrigger>
              </TabsList>
              <TabsContent value="description" className="mt-6">
                <div className="prose max-w-none">
                  <p>{product.description}</p>
                  <p>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies
                    tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl. Nullam auctor, nisl eget
                    ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.
                  </p>
                  <h3>Tính năng nổi bật</h3>
                  <ul>
                    <li>Hiệu năng mạnh mẽ với chip xử lý mới nhất</li>
                    <li>Màn hình sắc nét với độ phân giải cao</li>
                    <li>Thời lượng pin dài, sử dụng cả ngày</li>
                    <li>Camera chất lượng cao, chụp ảnh sắc nét</li>
                    <li>Thiết kế sang trọng, bền bỉ</li>
                  </ul>
                </div>
              </TabsContent>
              <TabsContent value="specifications" className="mt-6">
                <div className="rounded-lg border">
                  <div className="grid grid-cols-1 sm:grid-cols-2">
                    <div className="border-b p-4 sm:border-r">
                      <div className="text-sm font-medium">Màn hình</div>
                      <div className="mt-1 text-sm text-muted-foreground">6.7 inch, Super Retina XDR OLED</div>
                    </div>
                    <div className="border-b p-4">
                      <div className="text-sm font-medium">Chip xử lý</div>
                      <div className="mt-1 text-sm text-muted-foreground">A16 Bionic</div>
                    </div>
                    <div className="border-b p-4 sm:border-r">
                      <div className="text-sm font-medium">RAM</div>
                      <div className="mt-1 text-sm text-muted-foreground">8GB</div>
                    </div>
                    <div className=\"border-b p-4
