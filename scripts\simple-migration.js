const fs = require('fs');
const path = require('path');

// Simple migration script that works without better-sqlite3
console.log('🚀 Starting simple data migration...\n');

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

// Helper function to read JSON file
function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

// Validate data files
function validateDataFiles() {
  console.log('📁 Validating data files...');
  let allValid = true;
  
  for (const [name, filePath] of Object.entries(FILES)) {
    if (fs.existsSync(filePath)) {
      const data = readJsonFile(filePath);
      console.log(`✓ ${name}.json - ${data.length} records`);
    } else {
      console.log(`❌ ${name}.json - Missing`);
      allValid = false;
    }
  }
  
  return allValid;
}

// Create database directory and initialize
function initializeDatabase() {
  console.log('\n🗄️  Initializing database...');
  
  // Ensure data directory exists
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
    console.log('✓ Created data directory');
  }
  
  // Create a simple flag file to indicate migration status
  const migrationFlag = path.join(DATA_DIR, '.migration-completed');
  if (fs.existsSync(migrationFlag)) {
    console.log('⚠️  Migration already completed. Delete .migration-completed to re-run.');
    return false;
  }
  
  return true;
}

// Validate data relationships
function validateRelationships() {
  console.log('\n🔗 Validating data relationships...');
  
  const categories = readJsonFile(FILES.categories);
  const users = readJsonFile(FILES.users);
  const products = readJsonFile(FILES.products);
  const promotionalProducts = readJsonFile(FILES.promotionalProducts);
  const reviews = readJsonFile(FILES.reviews);
  const orders = readJsonFile(FILES.orders);
  const transactions = readJsonFile(FILES.transactions);
  
  const allProducts = [...products, ...promotionalProducts];
  
  // Check relationships
  const categoryIds = categories.map(c => c.id);
  const userIds = users.map(u => u.id);
  const productIds = allProducts.map(p => p.id);
  const orderIds = orders.map(o => o.id);
  
  let issues = 0;
  
  // Check product-category relationships
  const invalidProductCategories = allProducts.filter(p => 
    p.category && !categoryIds.includes(p.category.id || p.category.slug)
  );
  if (invalidProductCategories.length > 0) {
    console.log(`⚠️  ${invalidProductCategories.length} products have invalid category references`);
    issues++;
  }
  
  // Check review relationships
  const invalidReviewProducts = reviews.filter(r => !productIds.includes(r.productId));
  const invalidReviewUsers = reviews.filter(r => !userIds.includes(r.userId));
  if (invalidReviewProducts.length > 0) {
    console.log(`⚠️  ${invalidReviewProducts.length} reviews reference invalid products`);
    issues++;
  }
  if (invalidReviewUsers.length > 0) {
    console.log(`⚠️  ${invalidReviewUsers.length} reviews reference invalid users`);
    issues++;
  }
  
  // Check order relationships
  const invalidOrderUsers = orders.filter(o => !userIds.includes(o.userId));
  if (invalidOrderUsers.length > 0) {
    console.log(`⚠️  ${invalidOrderUsers.length} orders reference invalid users`);
    issues++;
  }
  
  // Check transaction relationships
  const invalidTransactionOrders = transactions.filter(t => !orderIds.includes(t.orderId));
  const invalidTransactionUsers = transactions.filter(t => !userIds.includes(t.userId));
  if (invalidTransactionOrders.length > 0) {
    console.log(`⚠️  ${invalidTransactionOrders.length} transactions reference invalid orders`);
    issues++;
  }
  if (invalidTransactionUsers.length > 0) {
    console.log(`⚠️  ${invalidTransactionUsers.length} transactions reference invalid users`);
    issues++;
  }
  
  if (issues === 0) {
    console.log('✓ All relationships are valid');
  }
  
  return issues === 0;
}

// Create summary statistics
function createSummary() {
  console.log('\n📊 Migration Summary:');
  
  const stats = {};
  for (const [name, filePath] of Object.entries(FILES)) {
    const data = readJsonFile(filePath);
    stats[name] = data.length;
    console.log(`   ${name}: ${data.length} records`);
  }
  
  const totalRecords = Object.values(stats).reduce((sum, count) => sum + count, 0);
  console.log(`   Total: ${totalRecords} records`);
  
  return stats;
}

// Mark migration as completed
function markMigrationCompleted() {
  const migrationFlag = path.join(DATA_DIR, '.migration-completed');
  const timestamp = new Date().toISOString();
  const summary = {
    completed: timestamp,
    status: 'success',
    message: 'Data validation and preparation completed successfully'
  };
  
  fs.writeFileSync(migrationFlag, JSON.stringify(summary, null, 2));
  console.log('\n✅ Migration preparation completed successfully!');
}

// Main migration function
function runMigration() {
  try {
    // Step 1: Validate data files
    if (!validateDataFiles()) {
      console.log('\n❌ Data validation failed. Please check missing files.');
      return false;
    }
    
    // Step 2: Initialize database
    if (!initializeDatabase()) {
      return true; // Already migrated
    }
    
    // Step 3: Validate relationships
    if (!validateRelationships()) {
      console.log('\n⚠️  Data has relationship issues but migration will continue.');
    }
    
    // Step 4: Create summary
    createSummary();
    
    // Step 5: Mark as completed
    markMigrationCompleted();
    
    console.log('\n🎉 Data is ready for use!');
    console.log('\nNext steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Test the APIs at http://localhost:3000');
    console.log('3. Check the admin panel for data management');
    
    return true;
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    return false;
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  const success = runMigration();
  process.exit(success ? 0 : 1);
}

module.exports = { runMigration };
