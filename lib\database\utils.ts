import { getDatabase } from './connection';

// Generic database utilities

export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export class DatabaseUtils {
  private db: any;

  constructor() {
    this.db = getDatabase();
  }

  // Generic select with pagination and ordering
  select<T>(
    table: string,
    where: Record<string, any> = {},
    options: QueryOptions = {}
  ): T[] {
    const { limit, offset, orderBy, orderDirection = 'ASC' } = options;
    
    let query = `SELECT * FROM ${table}`;
    const params: any[] = [];
    
    // Build WHERE clause
    const whereConditions = Object.entries(where).map(([key, value]) => {
      params.push(value);
      return `${key} = ?`;
    });
    
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }
    
    // Add ORDER BY
    if (orderBy) {
      query += ` ORDER BY ${orderBy} ${orderDirection}`;
    }
    
    // Add LIMIT and OFFSET
    if (limit) {
      query += ` LIMIT ${limit}`;
      if (offset) {
        query += ` OFFSET ${offset}`;
      }
    }
    
    const stmt = this.db.prepare(query);
    return stmt.all(...params) as T[];
  }

  // Generic insert
  insert<T>(table: string, data: Record<string, any>): T {
    const columns = Object.keys(data);
    const placeholders = columns.map(() => '?').join(', ');
    const values = Object.values(data);
    
    const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
    const stmt = this.db.prepare(query);
    
    const result = stmt.run(...values);
    
    // Return the inserted record
    const selectStmt = this.db.prepare(`SELECT * FROM ${table} WHERE rowid = ?`);
    return selectStmt.get(result.lastInsertRowid) as T;
  }

  // Generic update
  update<T>(
    table: string,
    data: Record<string, any>,
    where: Record<string, any>
  ): T | null {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    
    const query = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
    const stmt = this.db.prepare(query);
    
    const params = [...Object.values(data), ...Object.values(where)];
    const result = stmt.run(...params);
    
    if (result.changes === 0) {
      return null;
    }
    
    // Return the updated record
    const selectStmt = this.db.prepare(`SELECT * FROM ${table} WHERE ${whereClause}`);
    return selectStmt.get(...Object.values(where)) as T;
  }

  // Generic delete
  delete(table: string, where: Record<string, any>): boolean {
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    const query = `DELETE FROM ${table} WHERE ${whereClause}`;
    const stmt = this.db.prepare(query);
    
    const result = stmt.run(...Object.values(where));
    return result.changes > 0;
  }

  // Count records
  count(table: string, where: Record<string, any> = {}): number {
    let query = `SELECT COUNT(*) as count FROM ${table}`;
    const params: any[] = [];
    
    const whereConditions = Object.entries(where).map(([key, value]) => {
      params.push(value);
      return `${key} = ?`;
    });
    
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }
    
    const stmt = this.db.prepare(query);
    const result = stmt.get(...params) as { count: number };
    return result.count;
  }

  // Check if record exists
  exists(table: string, where: Record<string, any>): boolean {
    return this.count(table, where) > 0;
  }

  // Execute raw SQL
  raw<T>(query: string, params: any[] = []): T[] {
    const stmt = this.db.prepare(query);
    return stmt.all(...params) as T[];
  }

  // Execute raw SQL and get single result
  rawOne<T>(query: string, params: any[] = []): T | null {
    const stmt = this.db.prepare(query);
    return (stmt.get(...params) as T) || null;
  }

  // Execute raw SQL without return
  exec(query: string, params: any[] = []): void {
    const stmt = this.db.prepare(query);
    stmt.run(...params);
  }
}

// Export singleton instance
export const dbUtils = new DatabaseUtils();
