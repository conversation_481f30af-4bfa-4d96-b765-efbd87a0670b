const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

// Database setup
const DB_PATH = path.join(process.cwd(), 'data', 'store.db');
const dataDir = path.dirname(DB_PATH);

if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const db = new Database(DB_PATH);
db.pragma('foreign_keys = ON');
db.pragma('journal_mode = WAL');

// Initialize schema
const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql');
if (fs.existsSync(schemaPath)) {
  const schema = fs.readFileSync(schemaPath, 'utf-8');
  const statements = schema
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);
  
  for (const statement of statements) {
    try {
      db.exec(statement);
    } catch (error) {
      console.error('Error executing schema statement:', error.message);
    }
  }
}

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

// Helper function to read JSON file
function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

// Migration functions
function migrateCategories() {
  console.log('Migrating categories...');
  const categories = readJsonFile(FILES.categories);
  
  const stmt = db.prepare(`
    INSERT OR REPLACE INTO categories (id, name, slug, description, image, status, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  for (const category of categories) {
    try {
      stmt.run(
        category.id,
        category.name,
        category.slug,
        category.description,
        category.image,
        category.status || 'active',
        category.createdAt || new Date().toISOString()
      );
      console.log(`✓ Migrated category: ${category.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate category ${category.id}:`, error.message);
    }
  }
}

function migrateUsers() {
  console.log('Migrating users...');
  const users = readJsonFile(FILES.users);
  
  const userStmt = db.prepare(`
    INSERT OR REPLACE INTO users (id, name, email, password, phone, role, status, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  const addressStmt = db.prepare(`
    INSERT OR REPLACE INTO user_addresses (user_id, full_name, phone, address, city, district, ward, is_default, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  for (const user of users) {
    try {
      userStmt.run(
        user.id,
        user.name,
        user.email,
        user.password,
        user.phone || '',
        user.role || 'customer',
        user.status || 'active',
        user.createdAt || new Date().toISOString()
      );
      
      if (user.address) {
        addressStmt.run(
          user.id,
          user.address.fullName,
          user.address.phone,
          user.address.address,
          user.address.city,
          user.address.district,
          user.address.ward,
          1,
          new Date().toISOString()
        );
      }
      
      console.log(`✓ Migrated user: ${user.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate user ${user.id}:`, error.message);
    }
  }
}

function migrateProducts() {
  console.log('Migrating products...');
  
  const stmt = db.prepare(`
    INSERT OR REPLACE INTO products (id, name, description, price, original_price, image, images, category_id, stock, rating, review_count, sold_count, featured, is_promotional, promotion_ends, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  // Migrate regular products
  const products = readJsonFile(FILES.products);
  for (const product of products) {
    try {
      stmt.run(
        product.id,
        product.name,
        product.description,
        product.price,
        product.originalPrice || null,
        product.image,
        JSON.stringify(product.images || []),
        product.category?.id || product.category?.slug || 'unknown',
        product.stock || 0,
        product.rating || 0,
        product.reviewCount || 0,
        product.soldCount || 0,
        product.featured ? 1 : 0,
        0, // is_promotional
        null, // promotion_ends
        product.createdAt || new Date().toISOString()
      );
      console.log(`✓ Migrated product: ${product.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate product ${product.id}:`, error.message);
    }
  }
  
  // Migrate promotional products
  const promotionalProducts = readJsonFile(FILES.promotionalProducts);
  for (const product of promotionalProducts) {
    try {
      stmt.run(
        product.id,
        product.name,
        product.description,
        product.price,
        product.originalPrice || null,
        product.image,
        JSON.stringify(product.images || []),
        product.category?.id || product.category?.slug || 'unknown',
        product.stock || 0,
        product.rating || 0,
        product.reviewCount || 0,
        product.soldCount || 0,
        product.featured ? 1 : 0,
        1, // is_promotional
        product.promotionEnds || null,
        product.createdAt || new Date().toISOString()
      );
      console.log(`✓ Migrated promotional product: ${product.name}`);
    } catch (error) {
      console.error(`✗ Failed to migrate promotional product ${product.id}:`, error.message);
    }
  }
}

function migrateReviews() {
  console.log('Migrating reviews...');
  const reviews = readJsonFile(FILES.reviews);
  
  const stmt = db.prepare(`
    INSERT OR REPLACE INTO reviews (id, product_id, user_id, rating, comment, status, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  for (const review of reviews) {
    try {
      stmt.run(
        review.id,
        review.productId,
        review.userId,
        review.rating,
        review.comment,
        review.status || 'pending',
        review.createdAt || new Date().toISOString()
      );
      console.log(`✓ Migrated review: ${review.id}`);
    } catch (error) {
      console.error(`✗ Failed to migrate review ${review.id}:`, error.message);
    }
  }
}

function migrateOrders() {
  console.log('Migrating orders...');
  const orders = readJsonFile(FILES.orders);
  
  const orderStmt = db.prepare(`
    INSERT OR REPLACE INTO orders (id, user_id, status, total_amount, payment_method, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  const addressStmt = db.prepare(`
    INSERT OR REPLACE INTO order_shipping_addresses (order_id, full_name, phone, address, city, district, ward)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  const itemStmt = db.prepare(`
    INSERT OR REPLACE INTO order_items (id, order_id, product_id, quantity, price, product_name, product_image)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  for (const order of orders) {
    try {
      orderStmt.run(
        order.id,
        order.userId,
        order.status || 'pending',
        order.totalAmount,
        order.paymentMethod,
        order.createdAt || new Date().toISOString()
      );
      
      if (order.shippingAddress) {
        addressStmt.run(
          order.id,
          order.shippingAddress.fullName,
          order.shippingAddress.phone,
          order.shippingAddress.address,
          order.shippingAddress.city,
          order.shippingAddress.district,
          order.shippingAddress.ward
        );
      }
      
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          itemStmt.run(
            item.id,
            order.id,
            item.productId,
            item.quantity,
            item.price,
            item.product?.name || 'Unknown Product',
            item.product?.image || null
          );
        }
      }
      
      console.log(`✓ Migrated order: ${order.id}`);
    } catch (error) {
      console.error(`✗ Failed to migrate order ${order.id}:`, error.message);
    }
  }
}

function migrateTransactions() {
  console.log('Migrating transactions...');
  const transactions = readJsonFile(FILES.transactions);
  
  const stmt = db.prepare(`
    INSERT OR REPLACE INTO transactions (id, order_id, user_id, amount, status, payment_method, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  for (const transaction of transactions) {
    try {
      stmt.run(
        transaction.id,
        transaction.orderId,
        transaction.userId,
        transaction.amount,
        transaction.status || 'pending',
        transaction.paymentMethod,
        transaction.createdAt || new Date().toISOString()
      );
      console.log(`✓ Migrated transaction: ${transaction.id}`);
    } catch (error) {
      console.error(`✗ Failed to migrate transaction ${transaction.id}:`, error.message);
    }
  }
}

// Main migration function
function runMigration() {
  console.log('🚀 Starting data migration from JSON files to SQLite database...\n');
  
  try {
    // Clear existing data
    console.log('Clearing existing data...');
    db.exec('DELETE FROM transactions');
    db.exec('DELETE FROM order_items');
    db.exec('DELETE FROM order_shipping_addresses');
    db.exec('DELETE FROM orders');
    db.exec('DELETE FROM reviews');
    db.exec('DELETE FROM products');
    db.exec('DELETE FROM user_addresses');
    db.exec('DELETE FROM users');
    db.exec('DELETE FROM categories');
    console.log('✓ Existing data cleared\n');
    
    // Run migrations in order
    migrateCategories();
    console.log('');
    
    migrateUsers();
    console.log('');
    
    migrateProducts();
    console.log('');
    
    migrateReviews();
    console.log('');
    
    migrateOrders();
    console.log('');
    
    migrateTransactions();
    console.log('');
    
    console.log('🎉 Migration completed successfully!');
    
    // Print summary
    const stats = {
      categories: db.prepare('SELECT COUNT(*) as count FROM categories').get(),
      users: db.prepare('SELECT COUNT(*) as count FROM users').get(),
      products: db.prepare('SELECT COUNT(*) as count FROM products').get(),
      reviews: db.prepare('SELECT COUNT(*) as count FROM reviews').get(),
      orders: db.prepare('SELECT COUNT(*) as count FROM orders').get(),
      transactions: db.prepare('SELECT COUNT(*) as count FROM transactions').get()
    };
    
    console.log('\n📊 Migration Summary:');
    console.log(`   Categories: ${stats.categories.count}`);
    console.log(`   Users: ${stats.users.count}`);
    console.log(`   Products: ${stats.products.count}`);
    console.log(`   Reviews: ${stats.reviews.count}`);
    console.log(`   Orders: ${stats.orders.count}`);
    console.log(`   Transactions: ${stats.transactions.count}`);
    
    return true;
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    return false;
  } finally {
    db.close();
  }
}

// Run migration
if (require.main === module) {
  const success = runMigration();
  process.exit(success ? 0 : 1);
}
