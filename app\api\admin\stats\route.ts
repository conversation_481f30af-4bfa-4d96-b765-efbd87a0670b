import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET() {
  try {
    // Đọc các file JSON
    const usersPath = path.join(process.cwd(), 'data', 'users.json')
    const productsPath = path.join(process.cwd(), 'data', 'products.json')
    const ordersPath = path.join(process.cwd(), 'data', 'orders.json')
    const promotionalProductsPath = path.join(process.cwd(), 'data', 'promotionalProducts.json')

    const users = JSON.parse(fs.readFileSync(usersPath, 'utf8'))
    const products = JSON.parse(fs.readFileSync(productsPath, 'utf8'))
    const orders = JSON.parse(fs.readFileSync(ordersPath, 'utf8'))
    const promotionalProducts = JSON.parse(fs.readFileSync(promotionalProductsPath, 'utf8'))

    // Tính to<PERSON> thống kê
    const stats = {
      totalUsers: users.length,
      totalProducts: products.length + promotionalProducts.length,
      totalOrders: orders.length,
      totalRevenue: orders.reduce((sum: number, order: any) => sum + (order.totalAmount || 0), 0),
      pendingReviews: 0, // Tạm thời set là 0 vì chưa có dữ liệu reviews
      pendingProducts: products.filter((product: any) => product.status === 'pending').length,
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
} 