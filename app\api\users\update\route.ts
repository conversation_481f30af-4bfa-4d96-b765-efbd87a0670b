import { NextResponse } from 'next/server'
import { UserModel, UserAddressModel } from '../../../../lib/database/models'
import { withTransaction } from '../../../../lib/database/connection'

export async function PUT(request: Request) {
  try {
    const data = await request.json()
    const { id, name, email, phone, address } = data

    // Check if user exists
    const existingUser = UserModel.getById(id)
    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user in a transaction
    const updatedUser = await withTransaction(async () => {
      // Update user basic info
      const user = UserModel.update(id, {
        name,
        email,
        phone
      })

      // Update or create address if provided
      if (address) {
        const existingAddresses = UserAddressModel.getByUserId(id)

        if (existingAddresses.length > 0) {
          // Update existing default address
          const defaultAddress = existingAddresses.find(addr => addr.is_default) || existingAddresses[0]
          UserAddressModel.update(defaultAddress.id, {
            full_name: address.fullName,
            phone: address.phone,
            address: address.address,
            city: address.city,
            district: address.district,
            ward: address.ward
          })
        } else {
          // Create new address
          UserAddressModel.create({
            user_id: id,
            full_name: address.fullName,
            phone: address.phone,
            address: address.address,
            city: address.city,
            district: address.district,
            ward: address.ward,
            is_default: true
          })
        }
      }

      return user
    })

    // Return updated user info (without password)
    const { password: _, ...userWithoutPassword } = updatedUser || existingUser
    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}