import { readJsonFile, writeJsonFile } from './jsonUtils';
import type { User } from './types';

/**
 * Kiểm tra đăng nhập
 * @param email Email người dùng
 * @param password Mật khẩu
 * @returns User object nếu đăng nhập thành công, null nếu thất bại
 */
export async function login(email: string, password: string): Promise<User | null> {
  try {
    const users = await readJsonFile<User[]>('users.json');
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
      return null;
    }

    // Không trả về mật khẩu trong response
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
}

/**
 * Đăng ký tài khoản mới
 * @param userData Thông tin người dùng mới
 * @returns User object nếu đăng ký thành công, null nếu thất bại
 */
export async function register(userData: Omit<User, 'id' | 'createdAt'>): Promise<User | null> {
  try {
    const users = await readJsonFile<User[]>('users.json');
    
    // Kiểm tra email đã tồn tại
    if (users.some(u => u.email === userData.email)) {
      return null;
    }

    // Tạo user mới
    const newUser: User = {
      id: `user-${Date.now()}`,
      ...userData,
      createdAt: new Date().toISOString(),
      role: 'customer' // Mặc định là customer
    };

    // Thêm user mới vào danh sách
    users.push(newUser);
    await writeJsonFile('users.json', users);

    // Không trả về mật khẩu trong response
    const { password: _, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  } catch (error) {
    console.error('Registration error:', error);
    return null;
  }
}

/**
 * Kiểm tra email đã tồn tại chưa
 * @param email Email cần kiểm tra
 * @returns boolean
 */
export async function isEmailExists(email: string): Promise<boolean> {
  try {
    const users = await readJsonFile<User[]>('users.json');
    return users.some(u => u.email === email);
  } catch (error) {
    console.error('Email check error:', error);
    return false;
  }
}

/**
 * Cập nhật thông tin người dùng
 * @param userId ID người dùng
 * @param updateData Dữ liệu cần cập nhật
 * @returns User object nếu cập nhật thành công, null nếu thất bại
 */
export async function updateUser(userId: string, updateData: Partial<User>): Promise<User | null> {
  try {
    const users = await readJsonFile<User[]>('users.json');
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) {
      return null;
    }

    // Cập nhật thông tin
    users[userIndex] = {
      ...users[userIndex],
      ...updateData
    };

    await writeJsonFile('users.json', users);

    // Không trả về mật khẩu trong response
    const { password: _, ...userWithoutPassword } = users[userIndex];
    return userWithoutPassword;
  } catch (error) {
    console.error('Update user error:', error);
    return null;
  }
}

/**
 * Lấy thông tin người dùng theo ID
 * @param userId ID người dùng
 * @returns User object nếu tìm thấy, null nếu không tìm thấy
 */
export async function getUserById(userId: string): Promise<User | null> {
  try {
    const users = await readJsonFile<User[]>('users.json');
    const user = users.find(u => u.id === userId);
    
    if (!user) {
      return null;
    }

    // Không trả về mật khẩu trong response
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error('Get user error:', error);
    return null;
  }
}

/**
 * Xóa tài khoản người dùng
 * @param userId ID người dùng
 * @returns boolean
 */
export async function deleteUser(userId: string): Promise<boolean> {
  try {
    const users = await readJsonFile<User[]>('users.json');
    const filteredUsers = users.filter(u => u.id !== userId);
    
    if (filteredUsers.length === users.length) {
      return false; // Không tìm thấy user
    }

    await writeJsonFile('users.json', filteredUsers);
    return true;
  } catch (error) {
    console.error('Delete user error:', error);
    return false;
  }
} 