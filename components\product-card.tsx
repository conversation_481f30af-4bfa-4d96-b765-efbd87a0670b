"use client"

import { useState } from "react"
import Link from "next/link"
import { Heart, ShoppingCart, Star } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import type { Product } from "@/lib/types"

interface ProductCardProps {
  product: Product
}

export function ProductCard({ product }: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)

  const handleAddToCart = () => {
    toast({
      title: "Đã thêm vào giỏ hàng",
      description: `${product.name} đã được thêm vào giỏ hàng của bạn.`,
    })
  }

  const handleToggleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast({
      title: isWishlisted ? "Đã xóa khỏi danh sách yêu thích" : "Đã thêm vào danh sách yêu thích",
      description: isWishlisted
        ? `${product.name} đã được xóa khỏi danh sách yêu thích của bạn.`
        : `${product.name} đã được thêm vào danh sách yêu thích của bạn.`,
    })
  }

  const discountPercentage = product.originalPrice
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  // Determine if this is a special promotion product
  const isSpecialPromo = String(product.id).startsWith("promo-")

  return (
    <Card className="overflow-hidden group">
      <div className="relative">
        <Link href={`/product/${product.id}`}>
          <img
            src={product.image || "/placeholder.svg?height=300&width=300"}
            alt={product.name}
            width={300}
            height={300}
            className="h-[200px] w-full object-cover transition-transform group-hover:scale-105"
          />
        </Link>
        {product.originalPrice && (
          <Badge
            className={`absolute top-2 left-2 ${isSpecialPromo ? "bg-red-600 hover:bg-red-700" : "bg-red-500 hover:bg-red-600"}`}
          >
            -{discountPercentage}%
          </Badge>
        )}
        {isSpecialPromo && <Badge className="absolute top-10 left-2 bg-yellow-500 hover:bg-yellow-600">Hot Deal</Badge>}
        <Button
          variant="outline"
          size="icon"
          className={`absolute top-2 right-2 rounded-full ${isWishlisted ? "bg-red-100" : "bg-background"}`}
          onClick={handleToggleWishlist}
        >
          <Heart className={`h-4 w-4 ${isWishlisted ? "fill-red-500 text-red-500" : ""}`} />
          <span className="sr-only">Thêm vào yêu thích</span>
        </Button>
      </div>
      <CardContent className="p-4">
        <Link href={`/product/${product.id}`} className="hover:underline">
          <h3 className="font-semibold line-clamp-2 min-h-[48px]">{product.name}</h3>
        </Link>
        <div className="flex items-center gap-1 mt-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${i < product.rating ? "fill-primary text-primary" : "fill-muted text-muted"}`}
            />
          ))}
          <span className="text-xs text-muted-foreground ml-1">({product.reviewCount})</span>
        </div>
        <div className="mt-2 space-y-1">
          <div className="flex items-center gap-2">
            <span className="font-bold text-lg">{formatCurrency(product.price)}</span>
            {product.originalPrice && (
              <span className="text-sm text-muted-foreground line-through">
                {formatCurrency(product.originalPrice)}
              </span>
            )}
          </div>
          {product.stock <= 5 && product.stock > 0 && (
            <p className="text-xs text-red-500">Chỉ còn {product.stock} sản phẩm</p>
          )}
          {product.stock === 0 && <p className="text-xs text-red-500">Hết hàng</p>}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button className="w-full" onClick={handleAddToCart} disabled={product.stock === 0}>
          <ShoppingCart className="mr-2 h-4 w-4" />
          Thêm vào giỏ
        </Button>
      </CardFooter>
    </Card>
  )
}
