const fs = require('fs');
const path = require('path');

// Check current data status
console.log('📊 Current Data Status\n');

const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

// Check each data file
for (const [name, filePath] of Object.entries(FILES)) {
  const data = readJsonFile(filePath);
  console.log(`${name}: ${data.length} records`);
  
  if (name === 'products' && data.length > 0) {
    console.log(`  Sample product: ${data[0].name} - ${data[0].price.toLocaleString()} VND`);
  }
  
  if (name === 'categories' && data.length > 0) {
    console.log(`  Categories: ${data.map(c => c.name).join(', ')}`);
  }
}

// Check promotional products
const promoProducts = readJsonFile(FILES.promotionalProducts);
const regularProducts = readJsonFile(FILES.products);
const totalProducts = regularProducts.length + promoProducts.length;

console.log(`\nTotal products: ${totalProducts} (${regularProducts.length} regular + ${promoProducts.length} promotional)`);

// Check if migration flag exists
const migrationFlag = path.join(DATA_DIR, '.migration-completed');
if (fs.existsSync(migrationFlag)) {
  const flagData = JSON.parse(fs.readFileSync(migrationFlag, 'utf-8'));
  console.log(`\nMigration status: ✅ Completed at ${flagData.completed}`);
} else {
  console.log('\nMigration status: ❌ Not completed');
}

console.log('\n✅ Data check completed!');
