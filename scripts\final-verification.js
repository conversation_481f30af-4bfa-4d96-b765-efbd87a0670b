console.log('🔍 Final Verification of Tech Store Application\n');

async function testEndpoint(url, description) {
  try {
    const response = await fetch(url);
    const status = response.status;
    const statusText = status === 200 ? '✅' : '❌';
    console.log(`${statusText} ${description}: ${status}`);
    return status === 200;
  } catch (error) {
    console.log(`❌ ${description}: ERROR - ${error.message}`);
    return false;
  }
}

async function testLoginAPI() {
  try {
    console.log('\n🔐 Testing Login API...');
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    const success = response.status === 200;
    console.log(`${success ? '✅' : '❌'} Admin Login API: ${response.status}`);
    
    if (success) {
      const data = await response.json();
      console.log(`   Welcome ${data.name} (${data.role})`);
    }
    
    return success;
  } catch (error) {
    console.log(`❌ Admin Login API: ERROR - ${error.message}`);
    return false;
  }
}

async function runVerification() {
  console.log('Testing Core Pages...');
  
  const tests = [
    ['http://localhost:3000/', 'Homepage'],
    ['http://localhost:3000/login', 'Login Page'],
    ['http://localhost:3000/products', 'Products Page'],
    ['http://localhost:3000/products/laptop-001', 'Product Detail Page'],
  ];
  
  let pageTests = 0;
  for (const [url, description] of tests) {
    if (await testEndpoint(url, description)) {
      pageTests++;
    }
  }
  
  console.log('\nTesting API Endpoints...');
  
  const apiTests = [
    ['http://localhost:3000/api/products', 'Products API'],
    ['http://localhost:3000/api/products/laptop-001', 'Product Detail API'],
    ['http://localhost:3000/api/products/promotional', 'Promotional Products API'],
  ];
  
  let apiTestsPassed = 0;
  for (const [url, description] of apiTests) {
    if (await testEndpoint(url, description)) {
      apiTestsPassed++;
    }
  }
  
  // Test login API
  const loginSuccess = await testLoginAPI();
  
  // Summary
  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('========================');
  console.log(`Pages: ${pageTests}/${tests.length} working`);
  console.log(`APIs: ${apiTestsPassed}/${apiTests.length} working`);
  console.log(`Login: ${loginSuccess ? '✅' : '❌'} working`);
  
  const totalTests = tests.length + apiTests.length + 1;
  const totalPassed = pageTests + apiTestsPassed + (loginSuccess ? 1 : 0);
  
  console.log(`\nOverall: ${totalPassed}/${totalTests} tests passed`);
  
  if (totalPassed === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! The application is working correctly.');
    console.log('\n✅ FIXES COMPLETED:');
    console.log('   • Fixed Next.js 15 async params issues');
    console.log('   • Resolved ENOENT build errors');
    console.log('   • Fixed admin login functionality');
    console.log('   • All pages and APIs are working');
    console.log('\n🔑 Admin Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('\n🌐 Application URL: http://localhost:3000');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
}

runVerification();
