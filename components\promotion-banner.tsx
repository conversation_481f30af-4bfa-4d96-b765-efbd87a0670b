import Link from "next/link"
import { Tag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

export function PromotionBanner() {
  return (
    <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white py-3">
      <div className="container flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Tag className="h-5 w-5" />
          <span className="font-medium">Khuyến mãi đặc biệt - Giảm giá lên đến 20%</span>
        </div>
        <Button variant="secondary" size="sm" asChild>
          <Link href="/category/khuyen-mai">Xem ngay</Link>
        </Button>
      </div>
    </div>
  )
}
