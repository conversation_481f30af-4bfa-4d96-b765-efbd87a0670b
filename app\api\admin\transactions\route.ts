import { NextResponse } from "next/server"
import { TransactionModel, OrderModel, UserModel } from "../../../../lib/database/models"

// GET /api/admin/transactions
export async function GET() {
  try {
    const transactions = TransactionModel.getAll()

    // Enrich transactions with order and user information
    const enrichedTransactions = transactions.map(transaction => {
      const order = OrderModel.getById(transaction.order_id)
      const user = UserModel.getById(transaction.user_id)

      return {
        ...transaction,
        order: order ? {
          id: order.id,
          status: order.status,
          totalAmount: order.total_amount
        } : null,
        user: user ? {
          id: user.id,
          name: user.name,
          email: user.email
        } : null
      }
    })
    return NextResponse.json(enrichedTransactions)
  } catch (error) {
    console.error("Error reading transactions:", error)
    return NextResponse.json(
      { error: "Failed to read transactions" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/transactions/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const updatedTransaction = TransactionModel.update(id, { status })

    if (!updatedTransaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    return NextResponse.json(updatedTransaction)
  } catch (error) {
    console.error("Error updating transaction:", error)
    return NextResponse.json(
      { error: "Failed to update transaction" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/transactions/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const deleted = TransactionModel.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: "Transaction deleted successfully" })
  } catch (error) {
    console.error("Error deleting transaction:", error)
    return NextResponse.json(
      { error: "Failed to delete transaction" },
      { status: 500 }
    )
  }
}