import Link from "next/link"
import { ChevronRight, ShoppingBag } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { ProductCard } from "@/components/product-card"
import { SearchBar } from "@/components/search-bar"
import { getProducts } from "@/lib/data"

interface SearchPageProps {
  searchParams: { q: string }
}

export default function SearchPage({ searchParams }: SearchPageProps) {
  const query = searchParams.q || ""

  // In a real app, we would search the database
  // For demo purposes, we'll filter the mock products
  const products = getProducts()
  const searchResults = products.filter(
    (product) =>
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.description.toLowerCase().includes(query.toLowerCase()),
  )

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>
          <div className="ml-auto flex items-center gap-4">
            <SearchBar />
            <Link href="/cart">
              <Button variant="outline" size="icon">
                <ShoppingBag className="h-5 w-5" />
                <span className="sr-only">Giỏ hàng</span>
              </Button>
            </Link>
            <Link href="/login">
              <Button variant="outline">Đăng nhập</Button>
            </Link>
            <Link href="/register">
              <Button>Đăng ký</Button>
            </Link>
          </div>
        </div>
        <nav className="container flex h-12 items-center gap-6 text-sm">
          <Link href="/category/dien-thoai" className="font-medium transition-colors hover:text-primary">
            Điện thoại
          </Link>
          <Link href="/category/laptop" className="font-medium transition-colors hover:text-primary">
            Laptop
          </Link>
          <Link href="/category/may-tinh-bang" className="font-medium transition-colors hover:text-primary">
            Máy tính bảng
          </Link>
          <Link href="/category/phu-kien" className="font-medium transition-colors hover:text-primary">
            Phụ kiện
          </Link>
          <Link href="/category/khuyen-mai" className="font-medium transition-colors hover:text-primary">
            Khuyến mãi
          </Link>
        </nav>
      </header>
      <main className="flex-1">
        <div className="container py-10">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
            <Link href="/" className="hover:text-foreground">
              Trang chủ
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground">Tìm kiếm</span>
          </div>

          <div className="mb-8">
            <h1 className="text-3xl font-bold">Kết quả tìm kiếm: "{query}"</h1>
            <p className="text-muted-foreground mt-2">Tìm thấy {searchResults.length} sản phẩm</p>
          </div>

          {searchResults.length === 0 ? (
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold">Không tìm thấy sản phẩm nào</h2>
              <p className="text-muted-foreground mt-2">Vui lòng thử lại với từ khóa khác.</p>
              <Button asChild className="mt-6">
                <Link href="/">Quay lại trang chủ</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {searchResults.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}
        </div>
      </main>
      <footer className="w-full border-t bg-background">
        <div className="container py-6">
          <p className="text-center text-sm text-muted-foreground">© 2025 TechStore. Tất cả quyền được bảo lưu.</p>
        </div>
      </footer>
    </div>
  )
}
