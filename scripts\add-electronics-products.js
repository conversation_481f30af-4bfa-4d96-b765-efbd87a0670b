const fs = require('fs');
const path = require('path');

console.log('📱 Adding Electronics Products to Database...\n');

// Read current products
const productsPath = path.join(process.cwd(), 'data', 'products.json');
const promoProductsPath = path.join(process.cwd(), 'data', 'promotionalProducts.json');

let products = [];
let promoProducts = [];

try {
  products = JSON.parse(fs.readFileSync(productsPath, 'utf-8'));
  promoProducts = JSON.parse(fs.readFileSync(promoProductsPath, 'utf-8'));
} catch (error) {
  console.error('Error reading existing products:', error);
}

// Get next product ID
const allProducts = [...products, ...promoProducts];
const lastId = allProducts.length > 0 
  ? Math.max(...allProducts.map(p => {
      const match = p.id.match(/\d+$/);
      return match ? parseInt(match[0]) : 0;
    }))
  : 0;

let nextId = lastId + 1;

// Electronics products to add
const electronicsProducts = [
  // Smartphones
  {
    name: "iPhone 15 Pro Max",
    description: "Điện thoại thông minh cao cấp với chip A17 Pro, camera 48MP và màn hình Super Retina XDR 6.7 inch",
    price: 29990000,
    originalPrice: 32990000,
    image: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500&h=500&fit=crop",
    category: { id: "smartphones", slug: "smartphones" },
    stock: 25,
    rating: 4.8,
    reviewCount: 156,
    soldCount: 89,
    featured: true
  },
  {
    name: "Samsung Galaxy S24 Ultra",
    description: "Flagship Android với S Pen, camera 200MP và màn hình Dynamic AMOLED 2X 6.8 inch",
    price: 27990000,
    originalPrice: 30990000,
    image: "https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500&h=500&fit=crop",
    category: { id: "smartphones", slug: "smartphones" },
    stock: 30,
    rating: 4.7,
    reviewCount: 134,
    soldCount: 67,
    featured: true
  },
  {
    name: "Xiaomi 14 Ultra",
    description: "Camera phone hàng đầu với Leica, Snapdragon 8 Gen 3 và sạc nhanh 90W",
    price: 19990000,
    originalPrice: 22990000,
    image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500&h=500&fit=crop",
    category: { id: "smartphones", slug: "smartphones" },
    stock: 40,
    rating: 4.6,
    reviewCount: 98,
    soldCount: 45
  },
  
  // Laptops
  {
    name: "MacBook Pro 16 inch M3 Max",
    description: "Laptop chuyên nghiệp với chip M3 Max, 36GB RAM, 1TB SSD và màn hình Liquid Retina XDR",
    price: 89990000,
    originalPrice: 94990000,
    image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500&h=500&fit=crop",
    category: { id: "laptops", slug: "laptops" },
    stock: 15,
    rating: 4.9,
    reviewCount: 87,
    soldCount: 23,
    featured: true
  },
  {
    name: "Dell XPS 15 OLED",
    description: "Laptop cao cấp với màn hình OLED 4K, Intel Core i7-13700H và RTX 4060",
    price: 45990000,
    originalPrice: 49990000,
    image: "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=500&fit=crop",
    category: { id: "laptops", slug: "laptops" },
    stock: 20,
    rating: 4.7,
    reviewCount: 65,
    soldCount: 34
  },
  {
    name: "ASUS ROG Strix G16",
    description: "Gaming laptop với RTX 4070, Intel Core i7-13650HX và màn hình 165Hz",
    price: 35990000,
    originalPrice: 39990000,
    image: "https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=500&h=500&fit=crop",
    category: { id: "laptops", slug: "laptops" },
    stock: 18,
    rating: 4.6,
    reviewCount: 92,
    soldCount: 41
  },
  
  // Tablets
  {
    name: "iPad Pro 12.9 inch M2",
    description: "Tablet chuyên nghiệp với chip M2, màn hình Liquid Retina XDR và hỗ trợ Apple Pencil",
    price: 25990000,
    originalPrice: 28990000,
    image: "https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=500&h=500&fit=crop",
    category: { id: "tablets", slug: "tablets" },
    stock: 22,
    rating: 4.8,
    reviewCount: 76,
    soldCount: 38,
    featured: true
  },
  {
    name: "Samsung Galaxy Tab S9 Ultra",
    description: "Tablet Android cao cấp với màn hình AMOLED 14.6 inch và S Pen",
    price: 22990000,
    originalPrice: 25990000,
    image: "https://images.unsplash.com/photo-1561154464-82e9adf32764?w=500&h=500&fit=crop",
    category: { id: "tablets", slug: "tablets" },
    stock: 25,
    rating: 4.6,
    reviewCount: 54,
    soldCount: 29
  },
  
  // Headphones
  {
    name: "Sony WH-1000XM5",
    description: "Tai nghe chống ồn hàng đầu với chất lượng âm thanh Hi-Res và pin 30 giờ",
    price: 7990000,
    originalPrice: 8990000,
    image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop",
    category: { id: "audio", slug: "audio" },
    stock: 50,
    rating: 4.8,
    reviewCount: 234,
    soldCount: 156,
    featured: true
  },
  {
    name: "AirPods Pro 2nd Gen",
    description: "Tai nghe true wireless với chống ồn chủ động và chip H2",
    price: 5990000,
    originalPrice: 6490000,
    image: "https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=500&h=500&fit=crop",
    category: { id: "audio", slug: "audio" },
    stock: 60,
    rating: 4.7,
    reviewCount: 189,
    soldCount: 123
  },
  
  // Smart Watches
  {
    name: "Apple Watch Series 9",
    description: "Smartwatch với chip S9, màn hình Always-On Retina và tính năng sức khỏe tiên tiến",
    price: 9990000,
    originalPrice: 10990000,
    image: "https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=500&h=500&fit=crop",
    category: { id: "wearables", slug: "wearables" },
    stock: 35,
    rating: 4.7,
    reviewCount: 145,
    soldCount: 78,
    featured: true
  },
  {
    name: "Samsung Galaxy Watch 6 Classic",
    description: "Smartwatch Android với bezel xoay, GPS và theo dõi sức khỏe toàn diện",
    price: 7990000,
    originalPrice: 8990000,
    image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop",
    category: { id: "wearables", slug: "wearables" },
    stock: 40,
    rating: 4.5,
    reviewCount: 98,
    soldCount: 52
  },
  
  // Cameras
  {
    name: "Sony Alpha A7 IV",
    description: "Máy ảnh mirrorless full-frame với cảm biến 33MP và quay video 4K",
    price: 54990000,
    originalPrice: 59990000,
    image: "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=500&h=500&fit=crop",
    category: { id: "cameras", slug: "cameras" },
    stock: 12,
    rating: 4.9,
    reviewCount: 67,
    soldCount: 18,
    featured: true
  },
  {
    name: "Canon EOS R6 Mark II",
    description: "Máy ảnh mirrorless với cảm biến 24MP, chống rung 8 stop và quay video 4K 60p",
    price: 49990000,
    originalPrice: 54990000,
    image: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=500&h=500&fit=crop",
    category: { id: "cameras", slug: "cameras" },
    stock: 15,
    rating: 4.8,
    reviewCount: 45,
    soldCount: 12
  },
  
  // Gaming
  {
    name: "PlayStation 5",
    description: "Console game thế hệ mới với SSD tốc độ cao và ray tracing",
    price: ********,
    originalPrice: 13990000,
    image: "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=500&h=500&fit=crop",
    category: { id: "gaming", slug: "gaming" },
    stock: 20,
    rating: 4.8,
    reviewCount: 234,
    soldCount: 89,
    featured: true
  },
  {
    name: "Xbox Series X",
    description: "Console gaming 4K với 12 teraflops và Quick Resume",
    price: ********,
    originalPrice: ********,
    image: "https://images.unsplash.com/photo-*************-fbf93132d53d?w=500&h=500&fit=crop",
    category: { id: "gaming", slug: "gaming" },
    stock: 25,
    rating: 4.7,
    reviewCount: 187,
    soldCount: 67
  },
  
  // Accessories
  {
    name: "Anker PowerBank 20000mAh",
    description: "Sạc dự phòng dung lượng cao với sạc nhanh PD 22.5W",
    price: 1290000,
    originalPrice: 1490000,
    image: "https://images.unsplash.com/photo-*************-d0ae3d1e4b9e?w=500&h=500&fit=crop",
    category: { id: "accessories", slug: "accessories" },
    stock: 100,
    rating: 4.6,
    reviewCount: 456,
    soldCount: 234
  },
  {
    name: "Logitech MX Master 3S",
    description: "Chuột không dây cao cấp với cảm biến 8000 DPI và pin 70 ngày",
    price: 2290000,
    originalPrice: 2590000,
    image: "https://images.unsplash.com/photo-*************-7fd91fc51a46?w=500&h=500&fit=crop",
    category: { id: "accessories", slug: "accessories" },
    stock: 75,
    rating: 4.8,
    reviewCount: 234,
    soldCount: 145
  }
];

// Add products with generated IDs
const newProducts = electronicsProducts.map(product => ({
  id: `prod-${String(nextId++).padStart(3, '0')}`,
  ...product,
  images: [product.image],
  createdAt: new Date().toISOString()
}));

// Add to products array
products.push(...newProducts);

// Create some promotional products
const promoProductsToAdd = newProducts.slice(0, 5).map(product => ({
  ...product,
  id: `promo-${String(nextId++).padStart(3, '0')}`,
  price: Math.floor(product.price * 0.8), // 20% discount
  promotionEnds: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
}));

promoProducts.push(...promoProductsToAdd);

// Write back to files
try {
  fs.writeFileSync(productsPath, JSON.stringify(products, null, 2));
  fs.writeFileSync(promoProductsPath, JSON.stringify(promoProducts, null, 2));
  
  console.log(`✅ Successfully added ${newProducts.length} new products!`);
  console.log(`✅ Successfully added ${promoProductsToAdd.length} promotional products!`);
  console.log(`📊 Total products: ${products.length}`);
  console.log(`📊 Total promotional products: ${promoProducts.length}`);
  console.log('\n🎉 Electronics products added successfully!');
  
} catch (error) {
  console.error('❌ Error writing products:', error);
  process.exit(1);
}
