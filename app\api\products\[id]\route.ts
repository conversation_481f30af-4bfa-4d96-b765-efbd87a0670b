import { NextResponse } from "next/server"
import { ProductModel } from "../../../../lib/database/models"

// Transform database product to API format
function transformProduct(dbProduct: any) {
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: dbProduct.category_id,
      name: dbProduct.category_name || dbProduct.category_id,
      slug: dbProduct.category_id
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: <PERSON><PERSON><PERSON>(dbProduct.featured),
    createdAt: dbProduct.created_at,
    promotionEnds: dbProduct.promotion_ends
  }
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const product = ProductModel.getById(id)

    if (!product) {
      return NextResponse.json(
        { error: "Sản phẩm không tìm thấy." },
        { status: 404 }
      )
    }

    const transformedProduct = transformProduct(product)
    return NextResponse.json(transformedProduct)
  } catch (error) {
    console.error("Error fetching product details:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy chi tiết sản phẩm." },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const updatedProductData = await request.json()

    // Transform API data to database format
    const dbUpdateData: any = {}

    if (updatedProductData.name !== undefined) dbUpdateData.name = updatedProductData.name
    if (updatedProductData.description !== undefined) dbUpdateData.description = updatedProductData.description
    if (updatedProductData.price !== undefined) dbUpdateData.price = updatedProductData.price
    if (updatedProductData.originalPrice !== undefined) dbUpdateData.original_price = updatedProductData.originalPrice
    if (updatedProductData.image !== undefined) dbUpdateData.image = updatedProductData.image
    if (updatedProductData.images !== undefined) dbUpdateData.images = JSON.stringify(updatedProductData.images)
    if (updatedProductData.categoryId !== undefined) dbUpdateData.category_id = updatedProductData.categoryId
    if (updatedProductData.stock !== undefined) dbUpdateData.stock = updatedProductData.stock
    if (updatedProductData.rating !== undefined) dbUpdateData.rating = updatedProductData.rating
    if (updatedProductData.reviewCount !== undefined) dbUpdateData.review_count = updatedProductData.reviewCount
    if (updatedProductData.soldCount !== undefined) dbUpdateData.sold_count = updatedProductData.soldCount
    if (updatedProductData.featured !== undefined) dbUpdateData.featured = updatedProductData.featured
    if (updatedProductData.promotionEnds !== undefined) dbUpdateData.promotion_ends = updatedProductData.promotionEnds

    const updatedProduct = ProductModel.update(id, dbUpdateData)

    if (!updatedProduct) {
      return NextResponse.json(
        { error: "Sản phẩm không tìm thấy." },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: "Cập nhật sản phẩm thành công." })
  } catch (error) {
    console.error("Error updating product:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật sản phẩm." },
      { status: 500 }
    )
  }
}