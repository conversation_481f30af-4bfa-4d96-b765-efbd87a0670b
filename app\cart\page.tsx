"use client"

import { useState } from "react"
import Link from "next/link"
import { ChevronRight, Minus, Plus, ShoppingBag, Trash2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import { getProducts } from "@/lib/data"

export default function CartPage() {
  // For demo purposes, we'll use some mock cart items
  const mockProducts = getProducts({ limit: 3 })
  const [cartItems, setCartItems] = useState(
    mockProducts.map((product) => ({
      id: `cart-${product.id}`,
      product,
      quantity: 1,
    })),
  )

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return

    const product = cartItems.find((item) => item.id === id)?.product
    if (product && newQuantity > product.stock) {
      toast({
        title: "Số lượng không hợp lệ",
        description: `Chỉ còn ${product.stock} sản phẩm trong kho.`,
        variant: "destructive",
      })
      return
    }

    setCartItems((prev) => prev.map((item) => (item.id === id ? { ...item, quantity: newQuantity } : item)))
  }

  const removeItem = (id: string) => {
    setCartItems((prev) => prev.filter((item) => item.id !== id))
    toast({
      title: "Đã xóa sản phẩm",
      description: "Sản phẩm đã được xóa khỏi giỏ hàng.",
    })
  }

  const subtotal = cartItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0)
  const shipping = subtotal > 500000 ? 0 : 30000
  const total = subtotal + shipping

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>
          <div className="ml-auto flex items-center gap-4">
            <Link href="/login">
              <Button variant="outline">Đăng nhập</Button>
            </Link>
            <Link href="/register">
              <Button>Đăng ký</Button>
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-1">
        <div className="container py-10">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
            <Link href="/" className="hover:text-foreground">
              Trang chủ
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground">Giỏ hàng</span>
          </div>

          <h1 className="text-3xl font-bold mb-8">Giỏ hàng của bạn</h1>

          {cartItems.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground" />
              <h2 className="text-xl font-semibold mt-4">Giỏ hàng của bạn đang trống</h2>
              <p className="text-muted-foreground mt-2">Hãy thêm sản phẩm vào giỏ hàng để tiếp tục.</p>
              <Button asChild className="mt-6">
                <Link href="/products">Tiếp tục mua sắm</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-8 md:grid-cols-3">
              <div className="md:col-span-2">
                <div className="rounded-lg border">
                  <div className="p-6">
                    <h2 className="text-lg font-semibold">Sản phẩm ({cartItems.length})</h2>
                  </div>
                  <Separator />
                  {cartItems.map((item) => (
                    <div key={item.id} className="p-6">
                      <div className="flex gap-4">
                        <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border">
                          <img
                            src={item.product.image || "/placeholder.svg"}
                            alt={item.product.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div className="flex flex-1 flex-col">
                          <div className="flex justify-between">
                            <Link href={`/product/${item.product.id}`} className="hover:underline">
                              <h3 className="font-medium">{item.product.name}</h3>
                            </Link>
                            <p className="font-medium">{formatCurrency(item.product.price * item.quantity)}</p>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {formatCurrency(item.product.price)} / sản phẩm
                          </p>
                          <div className="mt-auto flex items-center justify-between">
                            <div className="flex items-center">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-r-none"
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              >
                                <Minus className="h-3 w-3" />
                                <span className="sr-only">Giảm</span>
                              </Button>
                              <div className="flex h-8 w-12 items-center justify-center border-y">{item.quantity}</div>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-l-none"
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                                <span className="sr-only">Tăng</span>
                              </Button>
                            </div>
                            <Button variant="ghost" size="sm" onClick={() => removeItem(item.id)}>
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Xóa</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                      <Separator className="mt-6" />
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <div className="rounded-lg border">
                  <div className="p-6">
                    <h2 className="text-lg font-semibold">Tóm tắt đơn hàng</h2>
                    <div className="mt-6 space-y-4">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tạm tính</span>
                        <span>{formatCurrency(subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Phí vận chuyển</span>
                        <span>{shipping === 0 ? "Miễn phí" : formatCurrency(shipping)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-medium">
                        <span>Tổng cộng</span>
                        <span>{formatCurrency(total)}</span>
                      </div>
                      <Button className="w-full" asChild>
                        <Link href="/checkout">Thanh toán</Link>
                      </Button>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href="/products">Tiếp tục mua sắm</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      <footer className="w-full border-t bg-background">
        <div className="container py-6">
          <p className="text-center text-sm text-muted-foreground">© 2025 TechStore. Tất cả quyền được bảo lưu.</p>
        </div>
      </footer>
    </div>
  )
}
