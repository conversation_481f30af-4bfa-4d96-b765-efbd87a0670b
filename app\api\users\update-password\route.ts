import { NextResponse } from 'next/server'
import { UserModel } from '../../../../lib/database/models'

export async function PUT(request: Request) {
  try {
    const data = await request.json()
    const { id, currentPassword, newPassword } = data

    // Find user
    const user = UserModel.getById(id)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check current password
    if (user.password !== currentPassword) {
      return NextResponse.json(
        { message: '<PERSON><PERSON>t khẩu hiện tại không đúng' },
        { status: 400 }
      )
    }

    // Update password
    UserModel.update(id, { password: newPassword })

    return NextResponse.json({ message: 'Password updated successfully' })
  } catch (error) {
    console.error('Error updating password:', error)
    return NextResponse.json(
      { error: 'Failed to update password' },
      { status: 500 }
    )
  }
}