"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import DashboardLayout from "../dashboard/layout"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface OrderItem {
  id: string
  orderId: string
  productId: string
  product: {
    id: string
    name: string
    price: number
    image: string
  }
  quantity: number
  price: number
}

interface Order {
  id: string
  userId: string
  items: OrderItem[]
  status: "pending" | "processing" | "delivered" | "cancelled"
  totalAmount: number
  shippingAddress: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
  paymentMethod: string
  createdAt: string
}

const getStatusBadge = (status: Order["status"]) => {
  switch (status) {
    case "pending":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Chờ xử lý</Badge>
    case "processing":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Đang xử lý</Badge>
    case "delivered":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Đã giao hàng</Badge>
    case "cancelled":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Đã hủy</Badge>
    default:
      return null
  }
}

const getStatusIcon = (status: Order["status"]) => {
  switch (status) {
    case "pending":
      return <Clock className="h-4 w-4 text-yellow-600" />
    case "processing":
      return <AlertCircle className="h-4 w-4 text-blue-600" />
    case "delivered":
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case "cancelled":
      return <XCircle className="h-4 w-4 text-red-600" />
    default:
      return null
  }
}

export default function OrdersPage() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      setUser(JSON.parse(userData))
    } else {
      router.push("/login")
    }
  }, [router])

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const userData = localStorage.getItem("user")
        if (!userData) {
          router.push("/login")
          return
        }

        const user = JSON.parse(userData)
        const response = await fetch(`/api/orders?userId=${user.id}`)
        if (!response.ok) {
          throw new Error('Failed to fetch orders')
        }
        const data = await response.json()
        setOrders(data)
      } catch (error) {
        console.error('Error fetching orders:', error)
        toast({
          title: "Lỗi",
          description: "Không thể tải danh sách đơn hàng. Vui lòng thử lại sau.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchOrders()
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem("user")
    setUser(null)
    router.push("/")
  }

  const handleDeleteOrder = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete order')
      }

      // Cập nhật danh sách đơn hàng sau khi xóa
      setOrders(orders.filter(order => order.id !== orderId))

      toast({
        title: "Thành công",
        description: "Đơn hàng đã được hủy thành công.",
      })
    } catch (error: any) {
      console.error('Error deleting order:', error)
      toast({
        title: "Lỗi",
        description: error.message || "Không thể hủy đơn hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout user={user || undefined} onLogout={handleLogout}>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Đơn hàng của tôi</h1>
        </div>

        {orders.length === 0 ? (
          <div className="text-center py-12 bg-muted rounded-lg">
            <p className="text-lg text-muted-foreground mb-4">Bạn chưa có đơn hàng nào</p>
            <Button asChild>
              <Link href="/products">Mua sắm ngay</Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Đơn hàng #{order.id}</CardTitle>
                      <CardDescription>
                        Đặt lúc {new Date(order.createdAt).toLocaleString('vi-VN')}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      {getStatusBadge(order.status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center gap-4">
                        <img
                          src={item.product.image}
                          alt={item.product.name}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <h3 className="font-medium">{item.product.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Số lượng: {item.quantity}
                          </p>
                        </div>
                        <p className="font-medium">
                          {(item.price * item.quantity).toLocaleString('vi-VN')}đ
                        </p>
                      </div>
                    ))}
                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-muted-foreground">Phương thức thanh toán</p>
                          <p className="font-medium">{order.paymentMethod}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-muted-foreground">Tổng tiền</p>
                          <p className="text-xl font-bold">{order.totalAmount.toLocaleString('vi-VN')}đ</p>
                        </div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <p className="text-sm text-muted-foreground mb-2">Địa chỉ giao hàng</p>
                      <div className="bg-muted p-4 rounded-lg">
                        <p className="font-medium">{order.shippingAddress.fullName}</p>
                        <p className="text-sm">{order.shippingAddress.phone}</p>
                        <p className="text-sm">
                          {order.shippingAddress.address}, {order.shippingAddress.ward}, {order.shippingAddress.district}, {order.shippingAddress.city}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
} 