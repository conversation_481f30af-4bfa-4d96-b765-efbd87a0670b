"use client"

import { use<PERSON>ffe<PERSON>, useState, use<PERSON>em<PERSON> } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import {
  ShoppingBag, User, LogOut, Package, Settings, Search, Filter,
  Star, Heart, ShoppingCart, Grid3X3, List, ChevronDown,
  SlidersHorizontal, X, ArrowUpDown, TrendingUp, Clock, DollarSign
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Sheet, Sheet<PERSON>ontent, SheetDescription, She<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/sheet"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface Category {
  id: string
  name: string
  slug: string
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice: number
  image: string
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured: boolean
  createdAt: string
}

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

export default function ProductsPage() {
  const [user, setUser] = useState<User | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Search and Filter States
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("newest")
  const [priceRange, setPriceRange] = useState([0, 100000000])
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [minRating, setMinRating] = useState(0)
  const [inStock, setInStock] = useState(false)
  const [featured, setFeatured] = useState(false)

  // UI States
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12)
  const [filtersOpen, setFiltersOpen] = useState(false)

  const router = useRouter()
  const searchParams = useSearchParams()

  // Initialize from URL params
  useEffect(() => {
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const sort = searchParams.get('sort')

    if (category) setSelectedCategory(category)
    if (search) setSearchQuery(search)
    if (sort) setSortBy(sort)
  }, [searchParams])

  useEffect(() => {
    // Get user info from localStorage
    const userStr = localStorage.getItem("user")
    if (userStr) {
      setUser(JSON.parse(userStr))
    }

    // Fetch products data
    const fetchProducts = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/products?limit=100')
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }

        const data = await response.json()
        setProducts(Array.isArray(data) ? data : [])

        // Set initial price range based on products
        if (data.length > 0) {
          const prices = data.map((p: Product) => p.price)
          const minPrice = Math.min(...prices)
          const maxPrice = Math.max(...prices)
          setPriceRange([minPrice, maxPrice])
        }
      } catch (error) {
        console.error('Error fetching products:', error)
        setError('Không thể tải danh sách sản phẩm')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  // Get unique brands from products
  const brands = useMemo(() => {
    const brandSet = new Set<string>()
    products.forEach(product => {
      // Extract brand from product name (first word)
      const brand = product.name.split(' ')[0]
      brandSet.add(brand)
    })
    return Array.from(brandSet).sort()
  }, [products])

  // Filter and sort products
  useEffect(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategory === "all" || product.category.id === selectedCategory || product.category.slug === selectedCategory
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1]
      const matchesBrand = selectedBrands.length === 0 || selectedBrands.some(brand =>
        product.name.toLowerCase().includes(brand.toLowerCase())
      )
      const matchesRating = product.rating >= minRating
      const matchesStock = !inStock || product.stock > 0
      const matchesFeatured = !featured || product.featured

      return matchesSearch && matchesCategory && matchesPrice && matchesBrand &&
             matchesRating && matchesStock && matchesFeatured
    })

    // Sort products
    switch (sortBy) {
      case "price-asc":
        filtered.sort((a, b) => a.price - b.price)
        break
      case "price-desc":
        filtered.sort((a, b) => b.price - a.price)
        break
      case "rating":
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case "popularity":
        filtered.sort((a, b) => b.soldCount - a.soldCount)
        break
      case "newest":
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        break
      case "oldest":
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        break
    }

    setFilteredProducts(filtered)
    setCurrentPage(1) // Reset to first page when filters change
  }, [products, searchQuery, selectedCategory, sortBy, priceRange, selectedBrands, minRating, inStock, featured])

  // Pagination logic
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentProducts = filteredProducts.slice(startIndex, endIndex)

  // Helper functions
  const handleLogout = () => {
    localStorage.removeItem("user")
    setUser(null)
    router.push("/")
  }

  const handleBrandToggle = (brand: string) => {
    setSelectedBrands(prev =>
      prev.includes(brand)
        ? prev.filter(b => b !== brand)
        : [...prev, brand]
    )
  }

  const clearAllFilters = () => {
    setSearchQuery("")
    setSelectedCategory("all")
    setSortBy("newest")
    setPriceRange([0, 100000000])
    setSelectedBrands([])
    setMinRating(0)
    setInStock(false)
    setFeatured(false)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price)
  }

  // Filter Sidebar Component
  const FilterSidebar = () => (
    <div className="space-y-6">
      {/* Clear Filters */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Bộ lọc</h3>
        <Button variant="ghost" size="sm" onClick={clearAllFilters}>
          <X className="h-4 w-4 mr-1" />
          Xóa tất cả
        </Button>
      </div>

      {/* Price Range Filter */}
      <div className="space-y-3">
        <h4 className="font-medium">Khoảng giá</h4>
        <div className="px-2">
          <Slider
            value={priceRange}
            onValueChange={setPriceRange}
            max={100000000}
            min={0}
            step={100000}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-muted-foreground mt-2">
            <span>{formatPrice(priceRange[0])}</span>
            <span>{formatPrice(priceRange[1])}</span>
          </div>
        </div>
      </div>

      {/* Brand Filter */}
      {brands.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium">Thương hiệu</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {brands.slice(0, 10).map(brand => (
              <div key={brand} className="flex items-center space-x-2">
                <Checkbox
                  id={brand}
                  checked={selectedBrands.includes(brand)}
                  onCheckedChange={() => handleBrandToggle(brand)}
                />
                <label htmlFor={brand} className="text-sm cursor-pointer">
                  {brand}
                </label>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Rating Filter */}
      <div className="space-y-3">
        <h4 className="font-medium">Đánh giá tối thiểu</h4>
        <div className="space-y-2">
          {[4, 3, 2, 1].map(rating => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={minRating === rating}
                onCheckedChange={() => setMinRating(minRating === rating ? 0 : rating)}
              />
              <label htmlFor={`rating-${rating}`} className="flex items-center text-sm cursor-pointer">
                {[...Array(rating)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                ))}
                <span className="ml-1">trở lên</span>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Additional Filters */}
      <div className="space-y-3">
        <h4 className="font-medium">Khác</h4>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="in-stock"
              checked={inStock}
              onCheckedChange={setInStock}
            />
            <label htmlFor="in-stock" className="text-sm cursor-pointer">
              Còn hàng
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="featured"
              checked={featured}
              onCheckedChange={setFeatured}
            />
            <label htmlFor="featured" className="text-sm cursor-pointer">
              Sản phẩm nổi bật
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>

          <div className="flex items-center gap-4">
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/avatars/01.png" alt={user.name} />
                      <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="cursor-pointer">
                      <User className="mr-2 h-4 w-4" />
                      <span>Hồ sơ</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/orders" className="cursor-pointer">
                      <Package className="mr-2 h-4 w-4" />
                      <span>Đơn hàng</span>
                    </Link>
                  </DropdownMenuItem>
                  {user.role === "admin" && (
                    <DropdownMenuItem asChild>
                      <Link href="/admin" className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Quản trị</span>
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Đăng xuất</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center gap-2">
                <Button variant="ghost" asChild>
                  <Link href="/login">Đăng nhập</Link>
                </Button>
                <Button asChild>
                  <Link href="/register">Đăng ký</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            {/* Header with Search and Controls */}
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <h1 className="text-3xl font-bold tracking-tighter">Tất cả sản phẩm</h1>
                  <p className="text-muted-foreground mt-2">
                    Tìm thấy {filteredProducts.length} sản phẩm
                  </p>
                </div>

                {/* Mobile Search */}
                <div className="md:hidden">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm sản phẩm..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Controls Bar */}
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div className="flex items-center gap-4">
                  {/* Mobile Filter Button */}
                  <Sheet open={filtersOpen} onOpenChange={setFiltersOpen}>
                    <SheetTrigger asChild>
                      <Button variant="outline" className="md:hidden">
                        <SlidersHorizontal className="h-4 w-4 mr-2" />
                        Bộ lọc
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80">
                      <SheetHeader>
                        <SheetTitle>Bộ lọc sản phẩm</SheetTitle>
                        <SheetDescription>
                          Tùy chỉnh tìm kiếm theo nhu cầu của bạn
                        </SheetDescription>
                      </SheetHeader>
                      <div className="mt-6">
                        <FilterSidebar />
                      </div>
                    </SheetContent>
                  </Sheet>

                  {/* View Mode Toggle */}
                  <div className="flex items-center border rounded-lg p-1">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Sort and Category */}
                <div className="flex items-center gap-4">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Danh mục" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả danh mục</SelectItem>
                      <SelectItem value="smartphones">Điện thoại</SelectItem>
                      <SelectItem value="laptops">Laptop</SelectItem>
                      <SelectItem value="tablets">Tablet</SelectItem>
                      <SelectItem value="audio">Âm thanh</SelectItem>
                      <SelectItem value="wearables">Thiết bị đeo</SelectItem>
                      <SelectItem value="cameras">Camera</SelectItem>
                      <SelectItem value="gaming">Gaming</SelectItem>
                      <SelectItem value="accessories">Phụ kiện</SelectItem>
                    </SelectContent>
                  </Select>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <ArrowUpDown className="h-4 w-4 mr-2" />
                        Sắp xếp
                        <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={() => setSortBy("newest")}>
                        <Clock className="h-4 w-4 mr-2" />
                        Mới nhất
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy("popularity")}>
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Phổ biến nhất
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy("rating")}>
                        <Star className="h-4 w-4 mr-2" />
                        Đánh giá cao
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy("price-asc")}>
                        <DollarSign className="h-4 w-4 mr-2" />
                        Giá tăng dần
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy("price-desc")}>
                        <DollarSign className="h-4 w-4 mr-2" />
                        Giá giảm dần
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex gap-8 mt-8">
              {/* Desktop Sidebar */}
              <div className="hidden md:block w-64 flex-shrink-0">
                <div className="sticky top-24">
                  <FilterSidebar />
                </div>
              </div>

              {/* Products Grid */}
              <div className="flex-1">
                {loading ? (
                  <div className={`grid gap-6 ${viewMode === 'grid' ? 'md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                    {[...Array(12)].map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardHeader>
                          <div className="aspect-square bg-gray-200 rounded-lg"></div>
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </CardHeader>
                        <CardContent>
                          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                          <div className="h-10 bg-gray-200 rounded"></div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : error ? (
                  <div className="text-center py-12">
                    <p className="text-red-500 mb-4">{error}</p>
                    <Button onClick={() => window.location.reload()}>Thử lại</Button>
                  </div>
                ) : filteredProducts.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="max-w-md mx-auto">
                      <div className="text-6xl mb-4">🔍</div>
                      <h3 className="text-lg font-semibold mb-2">Không tìm thấy sản phẩm</h3>
                      <p className="text-muted-foreground mb-4">
                        Thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm
                      </p>
                      <Button onClick={clearAllFilters}>Xóa tất cả bộ lọc</Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className={`grid gap-6 ${
                      viewMode === 'grid'
                        ? 'md:grid-cols-2 lg:grid-cols-3'
                        : 'grid-cols-1'
                    }`}>
                      {currentProducts.map((product) => (
                        <Card key={product.id} className="group hover:shadow-lg transition-shadow duration-300">
                          <CardHeader className="p-0">
                            <div className="relative overflow-hidden rounded-t-lg">
                              <img
                                src={product.image || '/placeholder.svg'}
                                alt={product.name}
                                className={`object-cover transition-transform duration-300 group-hover:scale-105 ${
                                  viewMode === 'grid' ? 'aspect-square' : 'aspect-video'
                                }`}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = '/placeholder.svg';
                                }}
                              />
                              {product.featured && (
                                <Badge className="absolute top-2 left-2 bg-primary">
                                  Nổi bật
                                </Badge>
                              )}
                              {product.originalPrice > product.price && (
                                <Badge className="absolute top-2 right-2 bg-red-500">
                                  -{Math.round((1 - product.price / product.originalPrice) * 100)}%
                                </Badge>
                              )}
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div className="flex gap-2">
                                  <Button size="icon" variant="secondary" className="rounded-full">
                                    <Heart className="h-4 w-4" />
                                  </Button>
                                  <Button size="icon" variant="secondary" className="rounded-full">
                                    <ShoppingCart className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                            <div className="p-4">
                              <CardTitle className="line-clamp-2 text-lg group-hover:text-primary transition-colors">
                                {product.name}
                              </CardTitle>
                              <CardDescription className="line-clamp-2 mt-2">
                                {product.description}
                              </CardDescription>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="flex items-center gap-1 mb-3">
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${
                                      i < Math.floor(product.rating)
                                        ? "text-yellow-400 fill-yellow-400"
                                        : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-muted-foreground">
                                ({product.reviewCount})
                              </span>
                              <Separator orientation="vertical" className="h-4" />
                              <span className="text-sm text-muted-foreground">
                                Đã bán {product.soldCount}
                              </span>
                            </div>

                            <div className="flex items-center gap-2 mb-4">
                              <p className="text-xl font-bold text-primary">
                                {formatPrice(product.price)}
                              </p>
                              {product.originalPrice > product.price && (
                                <p className="text-sm text-muted-foreground line-through">
                                  {formatPrice(product.originalPrice)}
                                </p>
                              )}
                            </div>

                            <div className="flex gap-2">
                              <Button className="flex-1" asChild>
                                <Link href={`/products/${product.id}`}>
                                  Xem chi tiết
                                </Link>
                              </Button>
                              <Button variant="outline" size="icon">
                                <ShoppingCart className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="flex justify-center items-center gap-2 mt-8">
                        <Button
                          variant="outline"
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                        >
                          Trước
                        </Button>

                        <div className="flex gap-1">
                          {[...Array(Math.min(5, totalPages))].map((_, i) => {
                            const pageNum = i + 1;
                            return (
                              <Button
                                key={pageNum}
                                variant={currentPage === pageNum ? "default" : "outline"}
                                size="sm"
                                onClick={() => setCurrentPage(pageNum)}
                              >
                                {pageNum}
                              </Button>
                            );
                          })}
                        </div>

                        <Button
                          variant="outline"
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                          disabled={currentPage === totalPages}
                        >
                          Sau
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            Built by{" "}
            <a
              href="#"
              target="_blank"
              rel="noreferrer"
              className="font-medium underline underline-offset-4"
            >
              TechStore
            </a>
            . All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
} 