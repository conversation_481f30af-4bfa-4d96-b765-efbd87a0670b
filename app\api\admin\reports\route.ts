import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

// GET /api/admin/reports
export async function GET() {
  try {
    // Đ<PERSON>c dữ liệu từ các file JSON
    const usersPath = path.join(process.cwd(), "data", "users.json")
    const productsPath = path.join(process.cwd(), "data", "products.json")
    const ordersPath = path.join(process.cwd(), "data", "orders.json")
    const promotionalProductsPath = path.join(process.cwd(), "data", "promotionalProducts.json")

    const users = JSON.parse(fs.readFileSync(usersPath, "utf8"))
    const products = JSON.parse(fs.readFileSync(productsPath, "utf8"))
    const orders = JSON.parse(fs.readFileSync(ordersPath, "utf8"))
    const promotionalProducts = JSON.parse(fs.readFileSync(promotionalProductsPath, "utf8"))

    // Tính toán tổng quan
    const totalRevenue = orders.reduce((sum: number, order: any) => sum + (order.totalAmount || 0), 0)
    const totalOrders = orders.length
    const totalUsers = users.length
    const totalProducts = products.length + promotionalProducts.length

    // Tính toán doanh thu theo tháng
    const revenueByMonth = orders.reduce((acc: any, order: any) => {
      const month = new Date(order.createdAt).toLocaleString("vi-VN", { month: "long" })
      acc[month] = (acc[month] || 0) + (order.totalAmount || 0)
      return acc
    }, {})

    const revenueByMonthArray = Object.entries(revenueByMonth).map(([month, revenue]) => ({
      month,
      revenue,
    }))

    // Tính toán trạng thái đơn hàng
    const ordersByStatus = orders.reduce((acc: any, order: any) => {
      acc[order.status] = (acc[order.status] || 0) + 1
      return acc
    }, {})

    const ordersByStatusArray = Object.entries(ordersByStatus).map(([status, count]) => ({
      status,
      count,
    }))

    // Tính toán sản phẩm bán chạy
    const productSales = orders.reduce((acc: any, order: any) => {
      order.items.forEach((item: any) => {
        acc[item.productId] = (acc[item.productId] || 0) + item.quantity
      })
      return acc
    }, {})

    const allProducts = [...products, ...promotionalProducts]
    const topProducts = Object.entries(productSales)
      .map(([productId, sales]) => {
        const product = allProducts.find((p: any) => p.id === productId)
        return product ? { name: product.name, sales } : null
      })
      .filter(Boolean)
      .sort((a: any, b: any) => b.sales - a.sales)
      .slice(0, 5)

    return NextResponse.json({
      totalRevenue,
      totalOrders,
      totalUsers,
      totalProducts,
      revenueByMonth: revenueByMonthArray,
      ordersByStatus: ordersByStatusArray,
      topProducts,
    })
  } catch (error) {
    console.error("Error generating reports:", error)
    return NextResponse.json(
      { error: "Failed to generate reports" },
      { status: 500 }
    )
  }
} 