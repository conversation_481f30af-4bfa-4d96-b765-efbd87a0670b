#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

// Helper function to read JSON file
function readJsonFile<T>(filePath: string): T[] {
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return [];
  }
}

// Validation functions
function validateCategories() {
  console.log('📋 Validating categories...');
  const categories = readJsonFile<any>(FILES.categories);
  
  console.log(`  - Total categories: ${categories.length}`);
  
  const requiredFields = ['id', 'name', 'slug'];
  const missingFields = categories.filter(cat => 
    requiredFields.some(field => !cat[field])
  );
  
  if (missingFields.length > 0) {
    console.warn(`  ⚠️  ${missingFields.length} categories missing required fields`);
  } else {
    console.log('  ✓ All categories have required fields');
  }
  
  return categories;
}

function validateUsers() {
  console.log('👥 Validating users...');
  const users = readJsonFile<any>(FILES.users);
  
  console.log(`  - Total users: ${users.length}`);
  
  const requiredFields = ['id', 'name', 'email', 'password'];
  const missingFields = users.filter(user => 
    requiredFields.some(field => !user[field])
  );
  
  if (missingFields.length > 0) {
    console.warn(`  ⚠️  ${missingFields.length} users missing required fields`);
  } else {
    console.log('  ✓ All users have required fields');
  }
  
  // Check for duplicate emails
  const emails = users.map(u => u.email);
  const duplicateEmails = emails.filter((email, index) => emails.indexOf(email) !== index);
  
  if (duplicateEmails.length > 0) {
    console.warn(`  ⚠️  Found ${duplicateEmails.length} duplicate emails`);
  } else {
    console.log('  ✓ No duplicate emails found');
  }
  
  return users;
}

function validateProducts() {
  console.log('📦 Validating products...');
  const products = readJsonFile<any>(FILES.products);
  const promotionalProducts = readJsonFile<any>(FILES.promotionalProducts);
  
  console.log(`  - Regular products: ${products.length}`);
  console.log(`  - Promotional products: ${promotionalProducts.length}`);
  console.log(`  - Total products: ${products.length + promotionalProducts.length}`);
  
  const allProducts = [...products, ...promotionalProducts];
  
  const requiredFields = ['id', 'name', 'price'];
  const missingFields = allProducts.filter(product => 
    requiredFields.some(field => !product[field])
  );
  
  if (missingFields.length > 0) {
    console.warn(`  ⚠️  ${missingFields.length} products missing required fields`);
  } else {
    console.log('  ✓ All products have required fields');
  }
  
  // Check for duplicate IDs
  const ids = allProducts.map(p => p.id);
  const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
  
  if (duplicateIds.length > 0) {
    console.warn(`  ⚠️  Found ${duplicateIds.length} duplicate product IDs`);
  } else {
    console.log('  ✓ No duplicate product IDs found');
  }
  
  return { products, promotionalProducts, allProducts };
}

function validateReviews() {
  console.log('⭐ Validating reviews...');
  const reviews = readJsonFile<any>(FILES.reviews);
  
  console.log(`  - Total reviews: ${reviews.length}`);
  
  const requiredFields = ['id', 'productId', 'userId', 'rating'];
  const missingFields = reviews.filter(review => 
    requiredFields.some(field => !review[field])
  );
  
  if (missingFields.length > 0) {
    console.warn(`  ⚠️  ${missingFields.length} reviews missing required fields`);
  } else {
    console.log('  ✓ All reviews have required fields');
  }
  
  // Check rating range
  const invalidRatings = reviews.filter(review => 
    review.rating < 1 || review.rating > 5
  );
  
  if (invalidRatings.length > 0) {
    console.warn(`  ⚠️  ${invalidRatings.length} reviews have invalid ratings`);
  } else {
    console.log('  ✓ All reviews have valid ratings (1-5)');
  }
  
  return reviews;
}

function validateOrders() {
  console.log('🛒 Validating orders...');
  const orders = readJsonFile<any>(FILES.orders);
  
  console.log(`  - Total orders: ${orders.length}`);
  
  const requiredFields = ['id', 'userId', 'totalAmount'];
  const missingFields = orders.filter(order => 
    requiredFields.some(field => !order[field])
  );
  
  if (missingFields.length > 0) {
    console.warn(`  ⚠️  ${missingFields.length} orders missing required fields`);
  } else {
    console.log('  ✓ All orders have required fields');
  }
  
  // Check if orders have items
  const ordersWithoutItems = orders.filter(order => 
    !order.items || !Array.isArray(order.items) || order.items.length === 0
  );
  
  if (ordersWithoutItems.length > 0) {
    console.warn(`  ⚠️  ${ordersWithoutItems.length} orders have no items`);
  } else {
    console.log('  ✓ All orders have items');
  }
  
  return orders;
}

function validateTransactions() {
  console.log('💳 Validating transactions...');
  const transactions = readJsonFile<any>(FILES.transactions);
  
  console.log(`  - Total transactions: ${transactions.length}`);
  
  const requiredFields = ['id', 'orderId', 'userId', 'amount'];
  const missingFields = transactions.filter(transaction => 
    requiredFields.some(field => !transaction[field])
  );
  
  if (missingFields.length > 0) {
    console.warn(`  ⚠️  ${missingFields.length} transactions missing required fields`);
  } else {
    console.log('  ✓ All transactions have required fields');
  }
  
  return transactions;
}

function validateRelationships(data: any) {
  console.log('🔗 Validating relationships...');
  
  const { categories, users, allProducts, reviews, orders, transactions } = data;
  
  // Check product-category relationships
  const categoryIds = categories.map((c: any) => c.id);
  const productsWithInvalidCategory = allProducts.filter((p: any) => 
    p.category && !categoryIds.includes(p.category.id || p.category.slug)
  );
  
  if (productsWithInvalidCategory.length > 0) {
    console.warn(`  ⚠️  ${productsWithInvalidCategory.length} products reference invalid categories`);
  } else {
    console.log('  ✓ All products reference valid categories');
  }
  
  // Check review-product relationships
  const productIds = allProducts.map((p: any) => p.id);
  const reviewsWithInvalidProduct = reviews.filter((r: any) => 
    !productIds.includes(r.productId)
  );
  
  if (reviewsWithInvalidProduct.length > 0) {
    console.warn(`  ⚠️  ${reviewsWithInvalidProduct.length} reviews reference invalid products`);
  } else {
    console.log('  ✓ All reviews reference valid products');
  }
  
  // Check review-user relationships
  const userIds = users.map((u: any) => u.id);
  const reviewsWithInvalidUser = reviews.filter((r: any) => 
    !userIds.includes(r.userId)
  );
  
  if (reviewsWithInvalidUser.length > 0) {
    console.warn(`  ⚠️  ${reviewsWithInvalidUser.length} reviews reference invalid users`);
  } else {
    console.log('  ✓ All reviews reference valid users');
  }
  
  // Check order-user relationships
  const ordersWithInvalidUser = orders.filter((o: any) => 
    !userIds.includes(o.userId)
  );
  
  if (ordersWithInvalidUser.length > 0) {
    console.warn(`  ⚠️  ${ordersWithInvalidUser.length} orders reference invalid users`);
  } else {
    console.log('  ✓ All orders reference valid users');
  }
  
  // Check transaction-order relationships
  const orderIds = orders.map((o: any) => o.id);
  const transactionsWithInvalidOrder = transactions.filter((t: any) => 
    !orderIds.includes(t.orderId)
  );
  
  if (transactionsWithInvalidOrder.length > 0) {
    console.warn(`  ⚠️  ${transactionsWithInvalidOrder.length} transactions reference invalid orders`);
  } else {
    console.log('  ✓ All transactions reference valid orders');
  }
}

// Main validation function
async function validateData() {
  console.log('🔍 Starting data validation...\n');
  
  try {
    const categories = validateCategories();
    console.log('');
    
    const users = validateUsers();
    console.log('');
    
    const productData = validateProducts();
    console.log('');
    
    const reviews = validateReviews();
    console.log('');
    
    const orders = validateOrders();
    console.log('');
    
    const transactions = validateTransactions();
    console.log('');
    
    validateRelationships({
      categories,
      users,
      ...productData,
      reviews,
      orders,
      transactions
    });
    
    console.log('\n✅ Data validation completed!');
    console.log('\n📊 Summary:');
    console.log(`  - Categories: ${categories.length}`);
    console.log(`  - Users: ${users.length}`);
    console.log(`  - Products: ${productData.allProducts.length}`);
    console.log(`  - Reviews: ${reviews.length}`);
    console.log(`  - Orders: ${orders.length}`);
    console.log(`  - Transactions: ${transactions.length}`);
    
    return true;
  } catch (error) {
    console.error('❌ Data validation failed:', error);
    return false;
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateData()
    .then((success) => {
      console.log(success ? '\n🎉 Data is ready for migration!' : '\n⚠️  Please fix data issues before migration.');
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Validation script failed:', error);
      process.exit(1);
    });
}
