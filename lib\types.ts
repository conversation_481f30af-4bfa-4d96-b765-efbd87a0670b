export interface Category {
  id: string
  name: string
  slug: string
}

export interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  image: string
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured: boolean
  createdAt: Date
  discount?: number
}

export interface Review {
  id: string
  userId: string
  productId: string
  rating: number
  comment: string
  createdAt: Date
  user: {
    id: string
    name: string
    avatar?: string
  }
}

export interface CartItem {
  id: string
  productId: string
  product: Product
  quantity: number
}

export interface Order {
  id: string
  userId: string
  items: OrderItem[]
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled"
  totalAmount: number
  shippingAddress: Address
  paymentMethod: string
  createdAt: Date
}

export interface OrderItem {
  id: string
  orderId: string
  productId: string
  product: Product
  quantity: number
  price: number
}

export interface Address {
  fullName: string
  phone: string
  address: string
  city: string
  district: string
  ward: string
}

export interface User {
  id: string
  name: string
  email: string
  password: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
  role: 'admin' | 'customer'
  createdAt: string
}
