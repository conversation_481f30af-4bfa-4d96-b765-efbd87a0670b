/**
 * API Migration Tests
 * 
 * This test suite verifies that all API endpoints work correctly
 * after migrating from JSON files to SQLite database.
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { getDatabase, closeDatabase } from '../lib/database/connection';
import {
  CategoryModel,
  UserModel,
  ProductModel,
  ReviewModel,
  OrderModel,
  TransactionModel
} from '../lib/database/models';

// Test data
const testUser = {
  name: 'Test User',
  email: '<EMAIL>',
  password: 'testpassword123',
  phone: '0123456789',
  role: 'customer' as const,
  status: 'active' as const
};

const testCategory = {
  id: 'test-cat-001',
  name: 'Test Category',
  slug: 'test-category',
  description: 'Test category description',
  status: 'active'
};

const testProduct = {
  id: 'test-prod-001',
  name: 'Test Product',
  description: 'Test product description',
  price: 100000,
  original_price: 120000,
  image: '/test-image.jpg',
  images: JSON.stringify(['/test-image.jpg']),
  category_id: 'test-cat-001',
  stock: 10,
  rating: 4.5,
  review_count: 5,
  sold_count: 2,
  featured: true,
  is_promotional: false,
  promotion_ends: null
};

describe('Database Models', () => {
  beforeAll(async () => {
    // Initialize database
    getDatabase();
  });

  afterAll(async () => {
    // Clean up test data
    try {
      ProductModel.delete(testProduct.id);
      CategoryModel.delete(testCategory.id);
      UserModel.getAll().forEach(user => {
        if (user.email.includes('test')) {
          UserModel.delete(user.id);
        }
      });
    } catch (error) {
      // Ignore cleanup errors
    }
    closeDatabase();
  });

  describe('CategoryModel', () => {
    test('should create and retrieve categories', () => {
      const created = CategoryModel.create(testCategory);
      expect(created.id).toBe(testCategory.id);
      expect(created.name).toBe(testCategory.name);

      const retrieved = CategoryModel.getById(testCategory.id);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.name).toBe(testCategory.name);

      const all = CategoryModel.getAll();
      expect(all.length).toBeGreaterThan(0);
    });

    test('should update categories', () => {
      const updated = CategoryModel.update(testCategory.id, { name: 'Updated Category' });
      expect(updated?.name).toBe('Updated Category');
    });
  });

  describe('UserModel', () => {
    test('should create and retrieve users', () => {
      const created = UserModel.create({
        ...testUser,
        id: 'test-user-001'
      });
      expect(created.email).toBe(testUser.email);
      expect(created.role).toBe(testUser.role);

      const retrieved = UserModel.getByEmail(testUser.email);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.name).toBe(testUser.name);

      const all = UserModel.getAll();
      expect(all.length).toBeGreaterThan(0);
    });

    test('should not allow duplicate emails', () => {
      expect(() => {
        UserModel.create({
          ...testUser,
          id: 'test-user-002'
        });
      }).toThrow();
    });
  });

  describe('ProductModel', () => {
    test('should create and retrieve products', () => {
      const created = ProductModel.create(testProduct);
      expect(created.id).toBe(testProduct.id);
      expect(created.name).toBe(testProduct.name);
      expect(created.price).toBe(testProduct.price);

      const retrieved = ProductModel.getById(testProduct.id);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.name).toBe(testProduct.name);

      const all = ProductModel.getAll();
      expect(all.length).toBeGreaterThan(0);
    });

    test('should filter featured products', () => {
      const featured = ProductModel.getFeatured(5);
      expect(Array.isArray(featured)).toBe(true);
      featured.forEach(product => {
        expect(product.featured).toBe(true);
      });
    });

    test('should search products', () => {
      const results = ProductModel.search('Test', 5);
      expect(Array.isArray(results)).toBe(true);
      if (results.length > 0) {
        expect(results[0].name.toLowerCase()).toContain('test');
      }
    });
  });

  describe('ReviewModel', () => {
    test('should create and retrieve reviews', () => {
      const user = UserModel.getByEmail(testUser.email);
      if (!user) throw new Error('Test user not found');

      const testReview = {
        id: 'test-review-001',
        product_id: testProduct.id,
        user_id: user.id,
        rating: 5,
        comment: 'Great product!',
        status: 'approved' as const
      };

      const created = ReviewModel.create(testReview);
      expect(created.rating).toBe(5);
      expect(created.comment).toBe('Great product!');

      const retrieved = ReviewModel.getById(testReview.id);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.rating).toBe(5);

      const productReviews = ReviewModel.getByProductId(testProduct.id);
      expect(productReviews.length).toBeGreaterThan(0);
    });
  });

  describe('OrderModel', () => {
    test('should create and retrieve orders', () => {
      const user = UserModel.getByEmail(testUser.email);
      if (!user) throw new Error('Test user not found');

      const testOrder = {
        id: 'test-order-001',
        user_id: user.id,
        status: 'pending' as const,
        total_amount: 100000,
        payment_method: 'credit_card'
      };

      const created = OrderModel.create(testOrder);
      expect(created.total_amount).toBe(100000);
      expect(created.status).toBe('pending');

      const retrieved = OrderModel.getById(testOrder.id);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.total_amount).toBe(100000);

      const userOrders = OrderModel.getByUserId(user.id);
      expect(userOrders.length).toBeGreaterThan(0);
    });
  });

  describe('TransactionModel', () => {
    test('should create and retrieve transactions', () => {
      const user = UserModel.getByEmail(testUser.email);
      if (!user) throw new Error('Test user not found');

      const testTransaction = {
        id: 'test-transaction-001',
        order_id: 'test-order-001',
        user_id: user.id,
        amount: 100000,
        status: 'completed' as const,
        payment_method: 'credit_card'
      };

      const created = TransactionModel.create(testTransaction);
      expect(created.amount).toBe(100000);
      expect(created.status).toBe('completed');

      const retrieved = TransactionModel.getById(testTransaction.id);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.amount).toBe(100000);

      const userTransactions = TransactionModel.getByUserId(user.id);
      expect(userTransactions.length).toBeGreaterThan(0);
    });
  });
});

describe('Data Integrity', () => {
  test('should maintain referential integrity', () => {
    // Test that products reference valid categories
    const products = ProductModel.getAll();
    const categories = CategoryModel.getAll();
    const categoryIds = categories.map(c => c.id);

    products.forEach(product => {
      expect(categoryIds).toContain(product.category_id);
    });
  });

  test('should handle JSON fields correctly', () => {
    const product = ProductModel.getById(testProduct.id);
    if (product && product.images) {
      expect(() => JSON.parse(product.images)).not.toThrow();
      const images = JSON.parse(product.images);
      expect(Array.isArray(images)).toBe(true);
    }
  });
});

describe('Performance', () => {
  test('should handle large queries efficiently', () => {
    const start = Date.now();
    const products = ProductModel.getAll({ limit: 100 });
    const end = Date.now();
    
    expect(end - start).toBeLessThan(1000); // Should complete within 1 second
    expect(products.length).toBeLessThanOrEqual(100);
  });

  test('should use indexes for common queries', () => {
    const start = Date.now();
    const featuredProducts = ProductModel.getFeatured(10);
    const end = Date.now();
    
    expect(end - start).toBeLessThan(500); // Should be fast with index
    expect(featuredProducts.length).toBeLessThanOrEqual(10);
  });
});
