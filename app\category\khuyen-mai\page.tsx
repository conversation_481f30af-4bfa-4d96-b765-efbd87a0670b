import Link from "next/link"
import { ChevronRight, ShoppingBag, Tag } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { ProductCard } from "@/components/product-card"
import { SearchBar } from "@/components/search-bar"
import { getPromotionalProducts, getCategories } from "@/lib/data"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function PromotionsPage() {
  const categories = getCategories()
  const promotionalProducts = getPromotionalProducts()

  // Group products by discount range
  const highDiscountProducts = promotionalProducts.filter((p) => p.discount && p.discount >= 15)
  const mediumDiscountProducts = promotionalProducts.filter((p) => p.discount && p.discount >= 10 && p.discount < 15)
  const specialProducts = promotionalProducts.filter((p) => p.id.startsWith("promo-"))

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>
          <div className="ml-auto flex items-center gap-4">
            <SearchBar />
            <Link href="/cart">
              <Button variant="outline" size="icon">
                <ShoppingBag className="h-5 w-5" />
                <span className="sr-only">Giỏ hàng</span>
              </Button>
            </Link>
            <Link href="/login">
              <Button variant="outline">Đăng nhập</Button>
            </Link>
            <Link href="/register">
              <Button>Đăng ký</Button>
            </Link>
          </div>
        </div>
        <nav className="container flex h-12 items-center gap-6 text-sm">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/category/${category.slug}`}
              className={`font-medium transition-colors hover:text-primary ${
                category.slug === "khuyen-mai" ? "text-primary" : ""
              }`}
            >
              {category.name}
            </Link>
          ))}
        </nav>
      </header>
      <main className="flex-1">
        <div className="container py-10">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
            <Link href="/" className="hover:text-foreground">
              Trang chủ
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground">Khuyến mãi</span>
          </div>

          <div className="mb-8">
            <div className="flex items-center gap-3">
              <Tag className="h-8 w-8 text-red-500" />
              <h1 className="text-3xl font-bold">Khuyến mãi đặc biệt</h1>
            </div>
            <p className="text-muted-foreground mt-2">Khám phá các sản phẩm giảm giá hấp dẫn với ưu đãi lên đến 20%</p>
          </div>

          <div className="space-y-12">
            {/* Special promotions section */}
            <section>
              <div className="bg-red-50 rounded-lg p-6 mb-8">
                <h2 className="text-2xl font-bold text-red-600 mb-2">Ưu đãi đặc biệt</h2>
                <p className="text-red-600">Giảm giá lên đến 20% cho các sản phẩm hot nhất</p>
              </div>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5">
                {specialProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            </section>

            {/* Tabbed discount sections */}
            <section>
              <Tabs defaultValue="high-discount" className="w-full">
                <TabsList className="w-full justify-start mb-6">
                  <TabsTrigger value="high-discount">Giảm giá cao (15% trở lên)</TabsTrigger>
                  <TabsTrigger value="medium-discount">Giảm giá tốt (10-15%)</TabsTrigger>
                </TabsList>

                <TabsContent value="high-discount">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                    {highDiscountProducts
                      .filter((p) => !p.id.startsWith("promo-"))
                      .slice(0, 8)
                      .map((product) => (
                        <ProductCard key={product.id} product={product} />
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="medium-discount">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                    {mediumDiscountProducts.slice(0, 8).map((product) => (
                      <ProductCard key={product.id} product={product} />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </section>

            {/* Promotional banners */}
            <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
                <h3 className="text-xl font-bold mb-2">Tuần lễ công nghệ</h3>
                <p className="mb-4">Giảm giá đặc biệt cho tất cả sản phẩm Apple</p>
                <Button variant="secondary" asChild>
                  <Link href="/category/dien-thoai">Khám phá ngay</Link>
                </Button>
              </div>
              <div className="bg-gradient-to-r from-amber-500 to-pink-500 rounded-lg p-6 text-white">
                <h3 className="text-xl font-bold mb-2">Phụ kiện giá sốc</h3>
                <p className="mb-4">Mua 2 tặng 1 cho tất cả phụ kiện</p>
                <Button variant="secondary" asChild>
                  <Link href="/category/phu-kien">Xem thêm</Link>
                </Button>
              </div>
            </section>

            {/* All promotional products */}
            <section>
              <h2 className="text-2xl font-bold mb-6">Tất cả sản phẩm khuyến mãi</h2>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                {promotionalProducts.slice(0, 12).map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
              {promotionalProducts.length > 12 && (
                <div className="flex justify-center mt-8">
                  <Button variant="outline" size="lg">
                    Xem thêm sản phẩm
                  </Button>
                </div>
              )}
            </section>
          </div>
        </div>
      </main>
      <footer className="w-full border-t bg-background">
        <div className="container py-6">
          <p className="text-center text-sm text-muted-foreground">© 2025 TechStore. Tất cả quyền được bảo lưu.</p>
        </div>
      </footer>
    </div>
  )
}
