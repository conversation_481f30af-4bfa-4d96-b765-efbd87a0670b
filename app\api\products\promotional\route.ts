import { NextResponse } from "next/server"
import { ProductModel } from "../../../../lib/database/models"

// Transform database product to API format
function transformProduct(dbProduct: any) {
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: dbProduct.category_id,
      name: dbProduct.category_name || dbProduct.category_id,
      slug: dbProduct.category_id
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: Boolean(dbProduct.featured),
    createdAt: dbProduct.created_at,
    promotionEnds: dbProduct.promotion_ends
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get("limit") || "10")

    // Get promotional products from database
    const products = ProductModel.getPromotional(limit)

    // Transform products to API format
    const transformedProducts = products.map(transformProduct)

    return NextResponse.json(transformedProducts)
  } catch (error) {
    console.error("Error fetching promotional products:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm khuyến mãi." },
      { status: 500 }
    )
  }
}