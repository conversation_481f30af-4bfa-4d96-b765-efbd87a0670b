#!/usr/bin/env tsx

import { getDatabase } from '../lib/database/connection';
import {
  CategoryModel,
  UserModel,
  ProductModel,
  ReviewModel,
  OrderModel,
  TransactionModel
} from '../lib/database/models';

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  timeout: 10000
};

// Helper function to make API requests
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  });
  
  const data = await response.json();
  return { response, data };
}

// Test database connection and basic operations
async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    const db = getDatabase();
    
    // Test basic queries
    const categories = CategoryModel.getAll();
    const users = UserModel.getAll();
    const products = ProductModel.getAll();
    const reviews = ReviewModel.getAll();
    const orders = OrderModel.getAll();
    const transactions = TransactionModel.getAll();
    
    console.log('✓ Database connection successful');
    console.log(`  - Categories: ${categories.length}`);
    console.log(`  - Users: ${users.length}`);
    console.log(`  - Products: ${products.length}`);
    console.log(`  - Reviews: ${reviews.length}`);
    console.log(`  - Orders: ${orders.length}`);
    console.log(`  - Transactions: ${transactions.length}`);
    
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Test product APIs
async function testProductAPIs() {
  console.log('\n🔍 Testing Product APIs...');
  
  try {
    // Test GET /api/products
    const { response: productsResponse, data: products } = await apiRequest('/api/products?limit=5');
    if (productsResponse.ok && Array.isArray(products)) {
      console.log('✓ GET /api/products - Success');
    } else {
      console.error('❌ GET /api/products - Failed');
      return false;
    }
    
    // Test GET /api/products/promotional
    const { response: promoResponse, data: promoProducts } = await apiRequest('/api/products/promotional?limit=3');
    if (promoResponse.ok && Array.isArray(promoProducts)) {
      console.log('✓ GET /api/products/promotional - Success');
    } else {
      console.error('❌ GET /api/products/promotional - Failed');
      return false;
    }
    
    // Test GET /api/products/[id] with first product
    if (products.length > 0) {
      const { response: productResponse, data: product } = await apiRequest(`/api/products/${products[0].id}`);
      if (productResponse.ok && product.id) {
        console.log('✓ GET /api/products/[id] - Success');
      } else {
        console.error('❌ GET /api/products/[id] - Failed');
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Product APIs test failed:', error);
    return false;
  }
}

// Test user and auth APIs
async function testUserAPIs() {
  console.log('\n🔍 Testing User & Auth APIs...');
  
  try {
    // Test user registration
    const newUser = {
      name: 'Test User',
      email: `test${Date.now()}@example.com`,
      password: 'testpassword123'
    };
    
    const { response: registerResponse, data: registeredUser } = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(newUser)
    });
    
    if (registerResponse.ok && registeredUser.id) {
      console.log('✓ POST /api/auth/register - Success');
      
      // Test user login
      const { response: loginResponse, data: loggedInUser } = await apiRequest('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: newUser.email,
          password: newUser.password
        })
      });
      
      if (loginResponse.ok && loggedInUser.id) {
        console.log('✓ POST /api/auth/login - Success');
        return true;
      } else {
        console.error('❌ POST /api/auth/login - Failed');
        return false;
      }
    } else {
      console.error('❌ POST /api/auth/register - Failed');
      return false;
    }
  } catch (error) {
    console.error('❌ User APIs test failed:', error);
    return false;
  }
}

// Test order APIs
async function testOrderAPIs() {
  console.log('\n🔍 Testing Order APIs...');
  
  try {
    // Get a test user
    const users = UserModel.getAll();
    if (users.length === 0) {
      console.error('❌ No users found for order testing');
      return false;
    }
    
    const testUser = users[0];
    
    // Test GET /api/orders
    const { response: ordersResponse, data: orders } = await apiRequest(`/api/orders?userId=${testUser.id}`);
    if (ordersResponse.ok && Array.isArray(orders)) {
      console.log('✓ GET /api/orders - Success');
      
      // Test GET /api/orders/[id] if orders exist
      if (orders.length > 0) {
        const { response: orderResponse, data: order } = await apiRequest(`/api/orders/${orders[0].id}`);
        if (orderResponse.ok && order.id) {
          console.log('✓ GET /api/orders/[id] - Success');
        } else {
          console.error('❌ GET /api/orders/[id] - Failed');
          return false;
        }
      }
      
      return true;
    } else {
      console.error('❌ GET /api/orders - Failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Order APIs test failed:', error);
    return false;
  }
}

// Test admin APIs
async function testAdminAPIs() {
  console.log('\n🔍 Testing Admin APIs...');
  
  try {
    // Test GET /api/admin/categories
    const { response: categoriesResponse, data: categories } = await apiRequest('/api/admin/categories');
    if (categoriesResponse.ok && Array.isArray(categories)) {
      console.log('✓ GET /api/admin/categories - Success');
    } else {
      console.error('❌ GET /api/admin/categories - Failed');
      return false;
    }
    
    // Test GET /api/admin/products
    const { response: productsResponse, data: products } = await apiRequest('/api/admin/products');
    if (productsResponse.ok && Array.isArray(products)) {
      console.log('✓ GET /api/admin/products - Success');
    } else {
      console.error('❌ GET /api/admin/products - Failed');
      return false;
    }
    
    // Test GET /api/admin/users
    const { response: usersResponse, data: users } = await apiRequest('/api/admin/users');
    if (usersResponse.ok && Array.isArray(users)) {
      console.log('✓ GET /api/admin/users - Success');
    } else {
      console.error('❌ GET /api/admin/users - Failed');
      return false;
    }
    
    // Test GET /api/admin/reviews
    const { response: reviewsResponse, data: reviews } = await apiRequest('/api/admin/reviews');
    if (reviewsResponse.ok && Array.isArray(reviews)) {
      console.log('✓ GET /api/admin/reviews - Success');
    } else {
      console.error('❌ GET /api/admin/reviews - Failed');
      return false;
    }
    
    // Test GET /api/admin/transactions
    const { response: transactionsResponse, data: transactions } = await apiRequest('/api/admin/transactions');
    if (transactionsResponse.ok && Array.isArray(transactions)) {
      console.log('✓ GET /api/admin/transactions - Success');
    } else {
      console.error('❌ GET /api/admin/transactions - Failed');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Admin APIs test failed:', error);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting API Migration Tests...\n');
  
  const tests = [
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Product APIs', fn: testProductAPIs },
    { name: 'User & Auth APIs', fn: testUserAPIs },
    { name: 'Order APIs', fn: testOrderAPIs },
    { name: 'Admin APIs', fn: testAdminAPIs }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ ${test.name} - Error:`, error);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✓ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Migration appears to be successful.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the migration.');
  }
  
  return failed === 0;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    });
}
